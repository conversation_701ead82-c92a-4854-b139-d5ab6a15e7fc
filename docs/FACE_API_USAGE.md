# 人脸识别API使用指南

本文档介绍如何使用人脸识别相关的API接口。

## 功能概述

人脸识别API基于InsightFace库，提供以下功能：

1. **人脸嵌入提取** - 从图片中提取人脸的512维特征向量
2. **人脸相似度比较** - 比较两张图片中人脸的相似度
3. **批量处理** - 支持批量提取嵌入和批量比较

## 支持的图片格式

- JPG/JPEG
- PNG
- BMP
- TIFF
- WebP

## API接口

### 1. 人脸嵌入提取

**接口**: `POST /face/embedding`

**描述**: 从单张图片中提取人脸嵌入向量

**参数**:
- `file`: 图片文件 (multipart/form-data)

**响应示例**:
```json
{
  "success": true,
  "embedding": [0.1234, -0.5678, ...],
  "embedding_shape": [512],
  "embedding_norm": 1.0,
  "face_count": 1,
  "model_name": "buffalo_l",
  "file_info": {
    "filename": "face.jpg",
    "size": 102400,
    "processing_time": 0.25
  }
}
```

**Python示例**:
```python
import requests

url = "http://localhost:9000/face/embedding"
with open("face.jpg", "rb") as f:
    files = {"file": f}
    response = requests.post(url, files=files)
    result = response.json()
    
if result["success"]:
    embedding = result["embedding"]
    print(f"提取到 {len(embedding)} 维嵌入向量")
```

### 2. 人脸相似度比较

**接口**: `POST /face/compare`

**描述**: 比较两张图片中人脸的相似度

**参数**:
- `file1`: 第一张图片 (multipart/form-data)
- `file2`: 第二张图片 (multipart/form-data)

**响应示例**:
```json
{
  "success": true,
  "similarity": 0.8234,
  "is_same_person": true,
  "threshold": 0.35,
  "model_name": "buffalo_l",
  "face_count_1": 1,
  "face_count_2": 1,
  "files": {
    "file1": "person1.jpg",
    "file2": "person2.jpg",
    "processing_time": 0.45
  }
}
```

**Python示例**:
```python
import requests

url = "http://localhost:9000/face/compare"
with open("face1.jpg", "rb") as f1, open("face2.jpg", "rb") as f2:
    files = {
        "file1": f1,
        "file2": f2
    }
    response = requests.post(url, files=files)
    result = response.json()
    
if result["success"]:
    similarity = result["similarity"]
    is_same = result["is_same_person"]
    print(f"相似度: {similarity:.4f}, 同一人: {is_same}")
```

### 3. 批量人脸嵌入提取

**接口**: `POST /batch/face/embedding`

**描述**: 批量提取多张图片的人脸嵌入向量

**参数**:
- `files`: 图片文件列表 (multipart/form-data)

**响应示例**:
```json
{
  "success": true,
  "total_files": 3,
  "successful_files": 2,
  "failed_files": 1,
  "results": [
    {
      "filename": "face1.jpg",
      "success": true,
      "embedding": [0.1234, -0.5678, ...],
      "embedding_shape": [512],
      "embedding_norm": 1.0,
      "face_count": 1
    },
    {
      "filename": "no_face.jpg",
      "success": false,
      "error": "图片中未检测到人脸"
    }
  ],
  "model_name": "buffalo_l",
  "total_duration": 1.23
}
```

### 4. 批量人脸比较

**接口**: `POST /batch/face/compare`

**描述**: 对多张图片进行两两人脸相似度比较

**参数**:
- `files`: 图片文件列表 (multipart/form-data, 至少2个文件)

**响应示例**:
```json
{
  "success": true,
  "total_comparisons": 3,
  "successful_comparisons": 2,
  "failed_comparisons": 1,
  "results": [
    {
      "file1": "face1.jpg",
      "file2": "face2.jpg",
      "success": true,
      "similarity": 0.8234,
      "is_same_person": true,
      "face_count_1": 1,
      "face_count_2": 1
    }
  ],
  "threshold": 0.35,
  "model_name": "buffalo_l"
}
```

## 配置参数

可以在 `config.py` 中调整以下参数：

```python
# 人脸识别配置
face_model_name: str = "buffalo_l"  # InsightFace模型名称
face_det_size: tuple = (640, 640)   # 人脸检测分辨率
face_ctx_id: int = 0                # GPU设备ID，-1为CPU
face_similarity_threshold: float = 0.35  # 人脸相似度阈值
```

## 错误处理

常见错误及解决方法：

1. **"图片中未检测到人脸"**
   - 确保图片中有清晰可见的人脸
   - 尝试使用更高质量的图片

2. **"不支持的图片格式"**
   - 使用支持的格式：jpg, jpeg, png, bmp, tiff, webp

3. **"人脸识别模型未初始化"**
   - 检查InsightFace模型是否正确安装
   - 确认GPU/CPU配置正确

4. **"文件过大"**
   - 图片文件大小不能超过50MB
   - 可以压缩图片或调整配置中的max_file_size

## 性能优化建议

1. **GPU加速**: 设置 `face_ctx_id = 0` 使用GPU（需要CUDA支持）
2. **检测分辨率**: 根据需要调整 `face_det_size`，更大的分辨率检测更准确但速度更慢
3. **批量处理**: 对于多张图片，使用批量接口比单独调用更高效
4. **图片预处理**: 使用适当大小和质量的图片可以提高处理速度

## 测试

运行测试脚本：

```bash
python test/test_face_api.py
```

确保在 `test/` 目录下放置测试图片：
- `test/face1.jpg`
- `test/face2.jpg`
- `test/face3.jpg`
