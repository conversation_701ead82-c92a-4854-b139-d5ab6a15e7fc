# 人脸识别功能集成说明

本文档说明了如何将人脸识别功能集成到现有的音频处理API中。

## 集成概述

我们成功将基于InsightFace的人脸识别功能集成到了现有的FastAPI应用中，提供了完整的人脸嵌入提取和相似度比较功能。

## 新增功能

### 1. 人脸嵌入提取
- **接口**: `POST /face/embedding`
- **功能**: 从单张图片中提取512维人脸特征向量
- **支持格式**: JPG, JPEG, PNG, BMP, TIFF, WebP

### 2. 人脸相似度比较
- **接口**: `POST /face/compare`
- **功能**: 比较两张图片中人脸的相似度
- **返回**: 相似度分数和是否为同一人的判断

### 3. 批量人脸处理
- **批量嵌入**: `POST /batch/face/embedding`
- **批量比较**: `POST /batch/face/compare`
- **功能**: 支持多张图片的批量处理

## 技术实现

### 核心依赖
```
opencv-python==4.8.1.78
insightface==0.7.3
onnxruntime==1.16.3
```

### 模型配置
- **模型**: buffalo_l (InsightFace预训练模型)
- **检测分辨率**: 640x640
- **特征维度**: 512维
- **相似度阈值**: 0.35

### 关键函数

1. **人脸检测和特征提取**
```python
def get_face_embeddings(img: np.ndarray) -> Tuple[List[np.ndarray], int]:
    faces = face_app.get(img)
    embeddings = []
    for face in faces:
        emb = getattr(face, "normed_embedding", None)
        if emb is None:
            emb = face.embedding
            emb = l2_normalize(emb)
        embeddings.append(emb.astype(np.float32))
    return embeddings, len(faces)
```

2. **相似度计算**
```python
def cosine_similarity(a: np.ndarray, b: np.ndarray) -> float:
    a = l2_normalize(a)
    b = l2_normalize(b)
    return float(np.dot(a, b))
```

## 文件修改清单

### 1. requirements.txt
添加了人脸识别相关依赖：
- opencv-python==4.8.1.78
- insightface==0.7.3
- onnxruntime==1.16.3

### 2. config.py
添加了人脸识别配置参数：
- face_model_name: 模型名称
- face_det_size: 检测分辨率
- face_ctx_id: GPU设备ID
- face_similarity_threshold: 相似度阈值
- allowed_image_extensions: 支持的图片格式

### 3. models.py
添加了人脸识别相关的数据模型：
- FaceEmbeddingResponse: 人脸嵌入响应
- FaceComparisonResponse: 人脸比较响应
- BatchFaceEmbeddingResponse: 批量嵌入响应
- BatchFaceComparisonResponse: 批量比较响应

### 4. main.py
集成了完整的人脸识别功能：
- 模型初始化
- 工具函数（图片验证、特征提取、相似度计算）
- API端点实现
- 错误处理

## 新增文件

### 1. 测试文件
- `test/test_face_api.py`: HTTP API测试脚本
- `test_face_simple.py`: 直接功能测试脚本

### 2. 文档文件
- `docs/FACE_API_USAGE.md`: 详细使用指南
- `docs/FACE_RECOGNITION_INTEGRATION.md`: 集成说明文档

### 3. 安装脚本
- `scripts/install_face_deps.sh`: 人脸识别依赖安装脚本

## 使用示例

### Python客户端示例
```python
import requests

# 人脸嵌入提取
url = "http://localhost:9000/face/embedding"
with open("face.jpg", "rb") as f:
    files = {"file": f}
    response = requests.post(url, files=files)
    result = response.json()
    embedding = result["embedding"]

# 人脸比较
url = "http://localhost:9000/face/compare"
with open("face1.jpg", "rb") as f1, open("face2.jpg", "rb") as f2:
    files = {"file1": f1, "file2": f2}
    response = requests.post(url, files=files)
    result = response.json()
    similarity = result["similarity"]
    is_same = result["is_same_person"]
```

### cURL示例
```bash
# 人脸嵌入提取
curl -X POST "http://localhost:9000/face/embedding" \
  -F "file=@face.jpg"

# 人脸比较
curl -X POST "http://localhost:9000/face/compare" \
  -F "file1=@face1.jpg" -F "file2=@face2.jpg"
```

## 性能特点

### 处理速度
- **GPU模式**: ~0.5秒/张图片
- **CPU模式**: ~2秒/张图片

### 内存使用
- **基础内存**: +1-2GB (模型加载)
- **GPU显存**: ~1-2GB (如果使用GPU)

### 准确性
- **模型**: buffalo_l (高精度模型)
- **特征维度**: 512维
- **相似度阈值**: 0.35 (可配置)

## 部署注意事项

### 1. 依赖安装
```bash
# 运行安装脚本
./scripts/install_face_deps.sh

# 或手动安装
pip install opencv-python insightface onnxruntime
```

### 2. GPU支持
- 需要CUDA环境
- 安装onnxruntime-gpu替代onnxruntime
- 设置face_ctx_id=0启用GPU

### 3. 模型下载
- 首次运行会自动下载模型文件
- 模型大小约100-200MB
- 确保网络连接正常

## 测试验证

### 1. 功能测试
```bash
# 直接功能测试
python test_face_simple.py

# HTTP API测试
python test/test_face_api.py
```

### 2. 性能测试
- 单张图片处理时间
- 批量处理吞吐量
- 内存使用情况

## 故障排除

### 常见问题

1. **"人脸识别模型未初始化"**
   - 检查InsightFace安装
   - 确认模型下载完成
   - 检查GPU/CPU配置

2. **"图片中未检测到人脸"**
   - 确保图片中有清晰人脸
   - 检查图片质量和分辨率
   - 尝试调整检测参数

3. **处理速度慢**
   - 启用GPU加速
   - 调整检测分辨率
   - 优化图片大小

### 日志调试
- 查看应用日志获取详细错误信息
- 使用debug模式获取更多调试信息

## 扩展建议

### 1. 功能扩展
- 多人脸检测和识别
- 人脸质量评估
- 年龄性别识别
- 表情识别

### 2. 性能优化
- 模型量化
- 批处理优化
- 缓存机制
- 异步处理

### 3. 安全增强
- 图片格式验证
- 文件大小限制
- 访问频率限制
- 数据加密存储
