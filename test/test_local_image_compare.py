import os
import cv2
import numpy as np
from typing import <PERSON>ple, List
from insightface.app import FaceAnalysis

def l2_normalize(v: np.ndarray, eps: float = 1e-12) -> np.ndarray:
    return v / (np.linalg.norm(v) + eps)

def cosine_similarity(a: np.ndarray, b: np.ndarray) -> float:
    a = l2_normalize(a)
    b = l2_normalize(b)
    return float(np.dot(a, b))

def init_insightface(model_name: str = "buffalo_l",
                     det_size: Tuple[int, int] = (640, 640),
                     ctx_id: int = 0) -> FaceAnalysis:
    """
    初始化 InsightFace 应用：
    - model_name: "buffalo_l" 为较高精度组合（包含人脸检测+识别）
    - det_size:   检测分辨率，视显存/内存情况调整
    - ctx_id:     0 为 GPU 0；若无 GPU 可设为 -1 使用 CPU
    """
    app = FaceAnalysis(name=model_name, allowed_modules=["detection", "recognition"])
    app.prepare(ctx_id=ctx_id, det_size=det_size)
    return app

def read_image(path: str) -> np.ndarray:
    img = cv2.imread(path)
    if img is None:
        raise FileNotFoundError(f"Cannot read image: {path}")
    return img

def get_face_embeddings(app: FaceAnalysis, img: np.ndarray) -> List[np.ndarray]:
    """
    对输入图像进行人脸检测/对齐，返回每张人脸的 embedding（512D）。
    """
    faces = app.get(img)  # 自动对齐 + 识别向量
    embeddings = []
    for f in faces:
        # f.normed_embedding 已经是L2归一化过的向量（有的版本叫 embedding，需要自己 normalize）
        emb = getattr(f, "normed_embedding", None)
        if emb is None:
            emb = f.embedding
            emb = l2_normalize(emb)
        embeddings.append(emb.astype(np.float32))
    return embeddings

def pick_primary_face(embeddings: List[np.ndarray]) -> np.ndarray:
    """
    根据需要挑选一张脸的 embedding。
    这里简单选择“第一张检测到的人脸”。也可改成按最大框面积/人脸质量分数挑选。
    """
    if not embeddings:
        raise ValueError("No face detected.")
    return embeddings[0]

def compare_two_images(img_path1: str,
                       img_path2: str,
                       threshold: float = 0.35,
                       ctx_id: int = 0) -> Tuple[float, bool]:
    """
    比对两张图片是否为同一人。
    - threshold：余弦距离/相似度的阈值建议
        * 使用余弦“相似度”（越大越像）时，一般阈值可设 ~0.3~0.5（不同模型、数据会有差异）
        * 你也可以改用欧式距离并设置距离阈值
    返回：(similarity, is_same_person)
    """
    app = init_insightface(ctx_id=ctx_id)

    img1 = read_image(img_path1)
    img2 = read_image(img_path2)

    embs1 = get_face_embeddings(app, img1)
    embs2 = get_face_embeddings(app, img2)

    emb1 = pick_primary_face(embs1)
    emb2 = pick_primary_face(embs2)

    sim = cosine_similarity(emb1, emb2)
    is_same = sim >= threshold
    return sim, is_same

if __name__ == "__main__":
    import argparse
    parser = argparse.ArgumentParser(description="Compare two face images with InsightFace+ArcFace")
    parser.add_argument("img1", type=str, help="Path to image 1")
    parser.add_argument("img2", type=str, help="Path to image 2")
    parser.add_argument("--threshold", type=float, default=0.35, help="Cosine similarity threshold")
    parser.add_argument("--cpu", action="store_true", help="Force CPU (ctx_id=-1)")
    args = parser.parse_args()

    ctx_id = -1 if args.cpu else 0
    sim, same = compare_two_images(args.img1, args.img2, threshold=args.threshold, ctx_id=ctx_id)
    print(f"Cosine similarity: {sim:.4f}")
    print(f"Same person? {'YES' if same else 'NO'} (threshold={args.threshold})")