#!/usr/bin/env python3
"""
智能人脸比对工具 - 带自动图片矫正功能
支持自动检测和矫正图片方向，提高人脸识别准确率
"""

import os
import cv2
import numpy as np
from typing import Tuple, List, Optional, Dict
from insightface.app import FaceAnalysis
import argparse

def l2_normalize(v: np.ndarray, eps: float = 1e-12) -> np.ndarray:
    return v / (np.linalg.norm(v) + eps)

def cosine_similarity(a: np.ndarray, b: np.ndarray) -> float:
    a = l2_normalize(a)
    b = l2_normalize(b)
    return float(np.dot(a, b))

class SmartFaceComparator:
    """智能人脸比对器"""
    
    def __init__(self, model_name: str = "buffalo_l", det_size: Tuple[int, int] = (640, 640), ctx_id: int = 0):
        """
        初始化人脸比对器
        - model_name: InsightFace模型名称
        - det_size: 检测分辨率
        - ctx_id: 设备ID (0=GPU, -1=CPU)
        """
        self.app = FaceAnalysis(name=model_name, allowed_modules=["detection", "recognition"])
        self.app.prepare(ctx_id=ctx_id, det_size=det_size)
        print(f"✅ 人脸识别模型初始化完成 (设备: {'GPU' if ctx_id >= 0 else 'CPU'})")
    
    def read_image(self, path: str) -> np.ndarray:
        """读取图像文件"""
        img = cv2.imread(path)
        if img is None:
            raise FileNotFoundError(f"无法读取图像: {path}")
        return img
    
    def rotate_image(self, img: np.ndarray, angle: float) -> np.ndarray:
        """旋转图像"""
        if angle == 90:
            return cv2.rotate(img, cv2.ROTATE_90_CLOCKWISE)
        elif angle == 180:
            return cv2.rotate(img, cv2.ROTATE_180)
        elif angle == 270:
            return cv2.rotate(img, cv2.ROTATE_90_COUNTERCLOCKWISE)
        else:
            return img
    
    def detect_text_orientation(self, img: np.ndarray) -> float:
        """通过文本方向检测图片旋转角度"""
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        
        # 边缘检测
        edges = cv2.Canny(gray, 50, 150, apertureSize=3)
        
        # 霍夫直线检测
        lines = cv2.HoughLines(edges, 1, np.pi/180, threshold=100)
        
        if lines is not None:
            angles = []
            for line in lines[:20]:  # 只取前20条线
                rho, theta = line[0]
                angle = theta * 180 / np.pi
                # 标准化角度到0-180度
                if angle > 90:
                    angle = angle - 180
                angles.append(angle)
            
            if angles:
                median_angle = np.median(angles)
                
                # 判断旋转角度
                if -45 <= median_angle <= 45:
                    return 0
                elif 45 < median_angle <= 135:
                    return 270
                elif median_angle > 135 or median_angle < -135:
                    return 180
                else:
                    return 90
        
        return 0
    
    def detect_face_orientation(self, img: np.ndarray) -> Tuple[float, Dict]:
        """通过人脸检测判断最佳图片方向"""
        best_angle = 0
        max_faces = 0
        max_confidence = 0
        detection_results = {}
        
        for angle in [0, 90, 180, 270]:
            test_img = self.rotate_image(img, angle)
            faces = self.app.get(test_img)
            
            face_count = len(faces)
            avg_confidence = np.mean([f.det_score for f in faces]) if faces else 0
            
            detection_results[angle] = {
                'face_count': face_count,
                'avg_confidence': avg_confidence,
                'faces': faces
            }
            
            # 选择最佳角度：优先考虑人脸数量，其次考虑置信度
            if (face_count > max_faces) or (face_count == max_faces and avg_confidence > max_confidence):
                max_faces = face_count
                max_confidence = avg_confidence
                best_angle = angle
        
        return best_angle, detection_results
    
    def auto_correct_orientation(self, img: np.ndarray, verbose: bool = True) -> Tuple[np.ndarray, float, Dict]:
        """
        自动矫正图片方向
        返回: (矫正后图片, 旋转角度, 检测详情)
        """
        if verbose:
            print("🔍 智能分析图片方向...")
        
        # 方法1: 人脸检测分析
        if verbose:
            print("  📱 人脸检测分析...")
        
        face_angle, detection_results = self.detect_face_orientation(img)
        
        # 如果检测到人脸，使用人脸检测结果
        if detection_results[face_angle]['face_count'] > 0:
            if verbose:
                print(f"  ✅ 人脸检测建议旋转: {face_angle}度 "
                      f"(检测到{detection_results[face_angle]['face_count']}张人脸, "
                      f"平均置信度: {detection_results[face_angle]['avg_confidence']:.3f})")
            
            corrected_img = self.rotate_image(img, face_angle)
            return corrected_img, face_angle, detection_results
        
        # 方法2: 文本方向分析
        if verbose:
            print("  📝 文本方向分析...")
        
        text_angle = self.detect_text_orientation(img)
        
        if text_angle != 0:
            if verbose:
                print(f"  ✅ 文本分析建议旋转: {text_angle}度")
            corrected_img = self.rotate_image(img, text_angle)
            return corrected_img, text_angle, detection_results
        
        if verbose:
            print("  ℹ️  图片方向正常，无需矫正")
        return img, 0, detection_results
    
    def extract_face_embeddings(self, img: np.ndarray, auto_correct: bool = True) -> List[np.ndarray]:
        """
        提取人脸特征向量
        auto_correct: 是否启用自动矫正
        """
        if auto_correct:
            # 使用自动矫正
            corrected_img, rotation_angle, detection_results = self.auto_correct_orientation(img)
            faces = self.app.get(corrected_img)
        else:
            # 直接处理原图
            faces = self.app.get(img)
        
        if not faces:
            return []
        
        embeddings = []
        for face in faces:
            emb = getattr(face, "normed_embedding", None)
            if emb is None:
                emb = face.embedding
                emb = l2_normalize(emb)
            embeddings.append(emb.astype(np.float32))
        
        return embeddings
    
    def compare_images(self, img_path1: str, img_path2: str, threshold: float = 0.35) -> Dict:
        """
        比较两张图片中的人脸
        返回详细的比较结果
        """
        print(f"🔄 开始比较图片...")
        print(f"  图片1: {img_path1}")
        print(f"  图片2: {img_path2}")
        print(f"  阈值: {threshold}")
        print("-" * 50)
        
        # 读取图片
        img1 = self.read_image(img_path1)
        img2 = self.read_image(img_path2)
        
        # 提取特征
        print("📷 处理图片1...")
        embeddings1 = self.extract_face_embeddings(img1)
        
        print("\n📷 处理图片2...")
        embeddings2 = self.extract_face_embeddings(img2)
        
        if not embeddings1:
            return {
                'success': False,
                'error': '图片1中未检测到人脸',
                'similarity': 0.0,
                'is_same_person': False
            }
        
        if not embeddings2:
            return {
                'success': False,
                'error': '图片2中未检测到人脸',
                'similarity': 0.0,
                'is_same_person': False
            }
        
        # 计算相似度（取第一张人脸）
        similarity = cosine_similarity(embeddings1[0], embeddings2[0])
        is_same_person = similarity >= threshold
        
        result = {
            'success': True,
            'similarity': similarity,
            'is_same_person': is_same_person,
            'threshold': threshold,
            'faces_detected': {
                'image1': len(embeddings1),
                'image2': len(embeddings2)
            }
        }
        
        print(f"\n📊 比较结果:")
        print(f"  相似度: {similarity:.4f}")
        print(f"  是否同一人: {'是' if is_same_person else '否'}")
        print(f"  检测到的人脸数: 图片1({len(embeddings1)}) vs 图片2({len(embeddings2)})")
        
        return result

def main():
    parser = argparse.ArgumentParser(description="智能人脸比对工具 - 自动矫正图片方向")
    parser.add_argument("img1", type=str, help="第一张图片路径")
    parser.add_argument("img2", type=str, help="第二张图片路径")
    parser.add_argument("--threshold", type=float, default=0.35, help="相似度阈值 (默认: 0.35)")
    parser.add_argument("--cpu", action="store_true", help="强制使用CPU")
    
    args = parser.parse_args()
    
    # 初始化比对器
    ctx_id = -1 if args.cpu else 0
    comparator = SmartFaceComparator(ctx_id=ctx_id)
    
    # 执行比较
    try:
        result = comparator.compare_images(args.img1, args.img2, args.threshold)
        
        if result['success']:
            print(f"\n🎯 最终结果: {'✅ 同一人' if result['is_same_person'] else '❌ 不同人'}")
        else:
            print(f"\n❌ 比较失败: {result['error']}")
            
    except Exception as e:
        print(f"\n💥 发生错误: {e}")

if __name__ == "__main__":
    main()
