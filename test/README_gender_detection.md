# 🎯 声音性别检测工具

## 📁 文件说明

- **`test_ultimate_gender_detector.py`** - 主要的声音性别检测工具
- **`gender_detection_summary.md`** - 详细的技术文档和开发历程
- **`test-voices/`** - 测试音频文件目录

## 🚀 快速使用

### 基本命令
```bash
# 激活虚拟环境
source ../venv/bin/activate

# 检测音频文件性别（自动模式，推荐）
python test_ultimate_gender_detector.py test-voices/1.mp3

# 指定检测方法
python test_ultimate_gender_detector.py test-voices/1.mp3 --method indian_optimized
```

### 可用方法
- `auto` - 自动选择最佳方法（默认，推荐）
- `indian_optimized` - 印度口音优化算法
- `advanced_features` - 高级特征分析
- `rules` - 基础规则方法

## 📊 测试结果

| 音频文件 | 检测结果 | 置信度 | 基频 | 状态 |
|----------|----------|--------|------|------|
| test-voices/1.mp3 | 女性 | 0.700 | 196.2Hz | ✅ 正确 |
| test-voices/2.mp3 | 男性 | 0.700 | 117.4Hz | ✅ 正确 |
| test-voices/3.mp3 | 男性 | 0.625 | 144.0Hz | ✅ 正确 |

## 🎯 特点

- ✅ **专门针对印度口音优化**
- ✅ **多方法集成，自动选择最佳结果**
- ✅ **高准确率（测试集100%）**
- ✅ **详细的分析报告**
- ✅ **容错机制，稳定可靠**

## 📋 输出示例

```
🎯 最终结果: 女性 (置信度: 0.700)
🔧 使用方法: indian_optimized
📊 详细信息:
   基频: 196.2Hz
   频谱质心: 1382Hz
   评分: 男性=1, 女性=7

📋 所有方法结果:
   ✅ indian_optimized: 女性 (0.700)
   ✅ advanced_features: 女性 (0.633)
   ✅ rules: 女性 (0.312)
```

## 🔧 依赖要求

主要依赖已安装在虚拟环境中：
- `librosa` - 音频处理
- `numpy` - 数值计算
- `speechbrain` - 深度学习模型（可选）

## 📖 更多信息

详细的技术文档和开发历程请查看：`gender_detection_summary.md`
