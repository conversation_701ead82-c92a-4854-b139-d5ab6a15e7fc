#!/usr/bin/env python3
"""
人脸识别API测试脚本
"""

import requests
import os
import sys
from pathlib import Path

# API基础URL
BASE_URL = "http://localhost:9000"

def test_face_embedding(image_path: str):
    """测试人脸嵌入提取"""
    print(f"\n🔍 测试人脸嵌入提取: {image_path}")
    
    if not os.path.exists(image_path):
        print(f"❌ 文件不存在: {image_path}")
        return None
    
    url = f"{BASE_URL}/face/embedding"
    
    with open(image_path, 'rb') as f:
        files = {'file': (os.path.basename(image_path), f, 'image/jpeg')}
        
        try:
            response = requests.post(url, files=files, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                if result['success']:
                    print(f"✅ 成功提取人脸嵌入")
                    print(f"   - 检测到人脸数量: {result['face_count']}")
                    print(f"   - 嵌入向量维度: {result['embedding_shape']}")
                    print(f"   - 向量范数: {result['embedding_norm']:.4f}")
                    print(f"   - 模型名称: {result['model_name']}")
                    print(f"   - 处理时间: {result['file_info']['processing_time']:.2f}s")
                    return result['embedding']
                else:
                    print(f"❌ 提取失败: {result.get('error', '未知错误')}")
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                print(f"   响应: {response.text}")
                
        except Exception as e:
            print(f"❌ 请求异常: {str(e)}")
    
    return None

def test_face_comparison(image1_path: str, image2_path: str):
    """测试人脸比较"""
    print(f"\n🔍 测试人脸比较:")
    print(f"   图片1: {image1_path}")
    print(f"   图片2: {image2_path}")
    
    if not os.path.exists(image1_path):
        print(f"❌ 文件不存在: {image1_path}")
        return
    
    if not os.path.exists(image2_path):
        print(f"❌ 文件不存在: {image2_path}")
        return
    
    url = f"{BASE_URL}/face/compare"
    
    with open(image1_path, 'rb') as f1, open(image2_path, 'rb') as f2:
        files = {
            'file1': (os.path.basename(image1_path), f1, 'image/jpeg'),
            'file2': (os.path.basename(image2_path), f2, 'image/jpeg')
        }
        
        try:
            response = requests.post(url, files=files, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                if result['success']:
                    print(f"✅ 人脸比较成功")
                    print(f"   - 相似度: {result['similarity']:.4f}")
                    print(f"   - 是否同一人: {'是' if result['is_same_person'] else '否'}")
                    print(f"   - 阈值: {result['threshold']}")
                    print(f"   - 图片1人脸数: {result['face_count_1']}")
                    print(f"   - 图片2人脸数: {result['face_count_2']}")
                    print(f"   - 模型名称: {result['model_name']}")
                    print(f"   - 处理时间: {result['files']['processing_time']:.2f}s")
                else:
                    print(f"❌ 比较失败: {result.get('error', '未知错误')}")
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                print(f"   响应: {response.text}")
                
        except Exception as e:
            print(f"❌ 请求异常: {str(e)}")

def test_batch_face_embedding(image_paths: list):
    """测试批量人脸嵌入提取"""
    print(f"\n🔍 测试批量人脸嵌入提取，共 {len(image_paths)} 张图片")
    
    url = f"{BASE_URL}/batch/face/embedding"
    
    files = []
    for path in image_paths:
        if os.path.exists(path):
            files.append(('files', (os.path.basename(path), open(path, 'rb'), 'image/jpeg')))
        else:
            print(f"⚠️  文件不存在，跳过: {path}")
    
    if not files:
        print("❌ 没有有效的图片文件")
        return
    
    try:
        response = requests.post(url, files=files, timeout=60)
        
        # 关闭文件句柄
        for _, (_, f, _) in files:
            f.close()
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 批量处理完成")
            print(f"   - 总文件数: {result['total_files']}")
            print(f"   - 成功: {result['successful_files']}")
            print(f"   - 失败: {result['failed_files']}")
            print(f"   - 总耗时: {result['total_duration']:.2f}s")
            
            for item in result['results']:
                status = "✅" if item['success'] else "❌"
                if item['success']:
                    print(f"   {status} {item['filename']}: 检测到 {item['face_count']} 张人脸")
                else:
                    print(f"   {status} {item['filename']}: {item['error']}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"   响应: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")
        # 确保关闭文件句柄
        for _, (_, f, _) in files:
            try:
                f.close()
            except:
                pass

def check_server():
    """检查服务器状态"""
    print("🔍 检查服务器状态...")
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 服务器运行正常")
            print(f"   - 应用: {result['app_name']}")
            print(f"   - 版本: {result['version']}")
            print(f"   - 状态: {result['status']}")
            return True
        else:
            print(f"❌ 服务器响应异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接服务器: {str(e)}")
        print("请确保服务器已启动 (python main.py)")
        return False

def main():
    """主函数"""
    print("🚀 人脸识别API测试")
    print("=" * 50)
    
    # 检查服务器
    if not check_server():
        return
    
    # 示例图片路径（需要用户提供实际的图片文件）
    test_images = [
        "test/face1.jpg",
        "test/face2.jpg", 
        "test/face3.jpg"
    ]
    
    print("\n📝 测试说明:")
    print("请将测试图片放在 test/ 目录下，命名为:")
    for img in test_images:
        print(f"   - {img}")
    
    # 检查测试图片是否存在
    available_images = [img for img in test_images if os.path.exists(img)]
    
    if len(available_images) >= 1:
        # 测试单张图片嵌入提取
        test_face_embedding(available_images[0])
        
        if len(available_images) >= 2:
            # 测试人脸比较
            test_face_comparison(available_images[0], available_images[1])
            
            # 测试批量处理
            test_batch_face_embedding(available_images)
    else:
        print("\n⚠️  未找到测试图片，请添加图片文件后重新运行测试")
        print("支持的格式: jpg, jpeg, png, bmp, tiff, webp")

if __name__ == "__main__":
    main()
