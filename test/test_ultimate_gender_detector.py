#!/usr/bin/env python3
"""
终极声音性别检测工具
集成多种方法，专门针对印度口音优化
"""

import os
import warnings
import numpy as np
from typing import Tuple, Dict, List
import argparse

warnings.filterwarnings("ignore")

class UltimateGenderDetector:
    """终极性别检测器 - 集成多种方法"""
    
    def __init__(self):
        print("🚀 初始化终极性别检测器...")
        self.methods = {}
        self._initialize_methods()
    
    def _initialize_methods(self):
        """初始化所有可用的检测方法"""
        
        # 1. 基于规则的方法（总是可用）
        self.methods['rules'] = True
        print("✅ 基于规则的方法 已加载")
        
        # 2. 印度口音优化方法
        self.methods['indian_optimized'] = True
        print("✅ 印度口音优化方法 已加载")
        
        # 3. SpeechBrain方法
        try:
            import speechbrain
            self.methods['speechbrain'] = True
            print("✅ SpeechBrain方法 已加载")
        except ImportError:
            self.methods['speechbrain'] = False
            print("❌ SpeechBrain方法 不可用")
        
        # 4. 高级特征方法
        self.methods['advanced_features'] = True
        print("✅ 高级特征方法 已加载")
    
    def detect_with_rules(self, audio_path: str) -> Tuple[str, float, Dict]:
        """基于规则的检测"""
        try:
            import librosa
            
            y, sr = librosa.load(audio_path, sr=16000)
            f0 = librosa.yin(y, fmin=50, fmax=400)
            f0_clean = f0[f0 > 0]
            
            if len(f0_clean) == 0:
                return "未知", 0.0, {'error': '无法提取基频'}
            
            f0_mean = np.mean(f0_clean)
            
            if f0_mean < 165:
                confidence = min(0.8, (165 - f0_mean) / 80)
                return "男性", confidence, {'f0_mean': f0_mean, 'method': 'rules'}
            else:
                confidence = min(0.8, (f0_mean - 165) / 100)
                return "女性", confidence, {'f0_mean': f0_mean, 'method': 'rules'}
                
        except Exception as e:
            raise Exception(f"规则检测失败: {e}")
    
    def detect_with_indian_optimized(self, audio_path: str) -> Tuple[str, float, Dict]:
        """印度口音优化检测"""
        try:
            import librosa
            
            y, sr = librosa.load(audio_path, sr=16000)
            
            # 提取多种特征
            f0 = librosa.yin(y, fmin=50, fmax=400)
            f0_clean = f0[f0 > 0]
            
            if len(f0_clean) == 0:
                return "未知", 0.0, {'error': '无法提取基频'}
            
            f0_mean = np.mean(f0_clean)
            f0_std = np.std(f0_clean)
            
            # 频谱质心
            spectral_centroids = librosa.feature.spectral_centroid(y=y, sr=sr)[0]
            spectral_centroid_mean = np.mean(spectral_centroids)
            
            # 印度口音优化的阈值
            indian_thresholds = {
                'male_f0_max': 150,
                'female_f0_min': 180,
                'male_spectral_max': 1500,
                'female_spectral_min': 1600,
                'female_f0_strong': 190,
                'male_f0_strong': 140,
            }
            
            # 评分系统
            male_score = 0
            female_score = 0
            
            # 基频评分（权重最高）
            if f0_mean >= indian_thresholds['female_f0_strong']:  # 190Hz以上
                female_score += 6
            elif f0_mean >= indian_thresholds['female_f0_min']:  # 180Hz以上
                female_score += 4
            elif f0_mean > 165:  # 165-180Hz
                female_score += 2
            elif f0_mean <= indian_thresholds['male_f0_strong']:  # 140Hz以下
                male_score += 6
            elif f0_mean <= indian_thresholds['male_f0_max']:  # 150Hz以下
                male_score += 4
            elif f0_mean < 165:  # 150-165Hz
                male_score += 2
            
            # 频谱质心评分（权重较低）
            if spectral_centroid_mean < indian_thresholds['male_spectral_max']:
                male_score += 1
            elif spectral_centroid_mean > indian_thresholds['female_spectral_min']:
                female_score += 1
            
            # 基频变化评分
            if f0_std > 50:  # 高变化通常是女性
                female_score += 1
            elif f0_std < 30:  # 低变化通常是男性
                male_score += 1
            
            total_score = male_score + female_score
            if total_score == 0:
                return "未知", 0.0, {'f0_mean': f0_mean}
            
            if male_score > female_score:
                confidence = male_score / (total_score + 2)  # 归一化
                return "男性", confidence, {
                    'f0_mean': f0_mean,
                    'spectral_centroid': spectral_centroid_mean,
                    'male_score': male_score,
                    'female_score': female_score,
                    'method': 'indian_optimized'
                }
            else:
                confidence = female_score / (total_score + 2)  # 归一化
                return "女性", confidence, {
                    'f0_mean': f0_mean,
                    'spectral_centroid': spectral_centroid_mean,
                    'male_score': male_score,
                    'female_score': female_score,
                    'method': 'indian_optimized'
                }
                
        except Exception as e:
            raise Exception(f"印度口音优化检测失败: {e}")
    
    def detect_with_advanced_features(self, audio_path: str) -> Tuple[str, float, Dict]:
        """高级特征检测"""
        try:
            import librosa
            
            y, sr = librosa.load(audio_path, sr=16000)
            
            # 提取多种特征
            features = {}
            
            # 基频特征
            f0 = librosa.yin(y, fmin=50, fmax=400)
            f0_clean = f0[f0 > 0]
            if len(f0_clean) > 0:
                features['f0_mean'] = np.mean(f0_clean)
                features['f0_std'] = np.std(f0_clean)
                features['f0_range'] = np.max(f0_clean) - np.min(f0_clean)
            else:
                return "未知", 0.0, {'error': '无法提取基频'}
            
            # 频谱特征
            spectral_centroids = librosa.feature.spectral_centroid(y=y, sr=sr)[0]
            features['spectral_centroid_mean'] = np.mean(spectral_centroids)
            features['spectral_centroid_std'] = np.std(spectral_centroids)
            
            # MFCC特征
            mfccs = librosa.feature.mfcc(y=y, sr=sr, n_mfcc=13)
            features['mfcc_mean'] = np.mean(mfccs[0])  # 第一个MFCC系数
            
            # 谐波比例
            harmonic, percussive = librosa.effects.hpss(y)
            features['harmonic_ratio'] = np.sum(harmonic**2) / (np.sum(harmonic**2) + np.sum(percussive**2))
            
            # 零交叉率
            zcr = librosa.feature.zero_crossing_rate(y)[0]
            features['zcr_mean'] = np.mean(zcr)
            
            # 综合评分
            score = 0
            confidence_factors = []
            
            # 基频评分（最重要）
            f0_mean = features['f0_mean']
            if f0_mean > 200:
                score += 3
                confidence_factors.append(0.9)
            elif f0_mean > 180:
                score += 2
                confidence_factors.append(0.8)
            elif f0_mean > 165:
                score += 1
                confidence_factors.append(0.6)
            elif f0_mean < 140:
                score -= 3
                confidence_factors.append(0.9)
            elif f0_mean < 155:
                score -= 2
                confidence_factors.append(0.8)
            elif f0_mean < 165:
                score -= 1
                confidence_factors.append(0.6)
            
            # 频谱质心评分
            if features['spectral_centroid_mean'] > 1600:
                score += 1
                confidence_factors.append(0.6)
            elif features['spectral_centroid_mean'] < 1400:
                score -= 1
                confidence_factors.append(0.6)
            
            # 基频变化评分
            if features['f0_std'] > 50:
                score += 1
                confidence_factors.append(0.5)
            elif features['f0_std'] < 30:
                score -= 1
                confidence_factors.append(0.5)
            
            # 计算置信度
            confidence = np.mean(confidence_factors) if confidence_factors else 0.5
            
            if score > 0:
                return "女性", min(confidence, 0.9), {**features, 'score': score, 'method': 'advanced_features'}
            elif score < 0:
                return "男性", min(confidence, 0.9), {**features, 'score': score, 'method': 'advanced_features'}
            else:
                return "未知", 0.5, {**features, 'score': score, 'method': 'advanced_features'}
                
        except Exception as e:
            raise Exception(f"高级特征检测失败: {e}")
    
    def detect(self, audio_path: str, method: str = "auto") -> Dict:
        """主检测函数"""
        if not os.path.exists(audio_path):
            return {
                'success': False,
                'error': f'文件不存在: {audio_path}'
            }
        
        print(f"🎵 分析音频: {audio_path}")
        print(f"🔧 检测方法: {method}")
        
        results = []
        
        if method == "auto":
            # 自动模式：尝试所有方法
            methods_to_try = [
                ('indian_optimized', self.detect_with_indian_optimized),
                ('advanced_features', self.detect_with_advanced_features),
                ('rules', self.detect_with_rules)
            ]
            
            for method_name, method_func in methods_to_try:
                if not self.methods.get(method_name, False):
                    continue
                
                try:
                    gender, confidence, details = method_func(audio_path)
                    results.append({
                        'method': method_name,
                        'gender': gender,
                        'confidence': confidence,
                        'details': details
                    })
                    print(f"✅ {method_name}: {gender} (置信度: {confidence:.3f})")
                    
                except Exception as e:
                    print(f"❌ {method_name}: 失败 - {e}")
                    results.append({
                        'method': method_name,
                        'gender': '未知',
                        'confidence': 0.0,
                        'error': str(e)
                    })
        
        else:
            # 指定方法
            method_map = {
                'rules': self.detect_with_rules,
                'indian_optimized': self.detect_with_indian_optimized,
                'advanced_features': self.detect_with_advanced_features
            }
            
            if method not in method_map:
                return {
                    'success': False,
                    'error': f'未知方法: {method}'
                }
            
            try:
                gender, confidence, details = method_map[method](audio_path)
                results.append({
                    'method': method,
                    'gender': gender,
                    'confidence': confidence,
                    'details': details
                })
            except Exception as e:
                return {
                    'success': False,
                    'error': str(e)
                }
        
        # 选择最佳结果
        if not results:
            return {
                'success': False,
                'error': '所有检测方法都失败了'
            }
        
        # 优先选择成功的结果，按置信度排序
        successful_results = [r for r in results if r['gender'] != '未知']
        
        if successful_results:
            # 按方法优先级和置信度排序
            method_priority = {'indian_optimized': 3, 'advanced_features': 2, 'rules': 1}
            best_result = max(successful_results, 
                            key=lambda x: (method_priority.get(x['method'], 0), x['confidence']))
        else:
            best_result = results[0]
        
        return {
            'success': best_result['gender'] != '未知',
            'gender': best_result['gender'],
            'confidence': best_result['confidence'],
            'method_used': best_result['method'],
            'all_results': results,
            'details': best_result.get('details', {}),
            'error': best_result.get('error')
        }

def main():
    parser = argparse.ArgumentParser(description="终极声音性别检测工具")
    parser.add_argument("audio_file", help="音频文件路径")
    parser.add_argument("--method", "-m", 
                       choices=["auto", "rules", "indian_optimized", "advanced_features"], 
                       default="auto", help="检测方法")
    
    args = parser.parse_args()
    
    detector = UltimateGenderDetector()
    result = detector.detect(args.audio_file, method=args.method)
    
    print("\n" + "="*60)
    if result['success']:
        print(f"🎯 最终结果: {result['gender']} (置信度: {result['confidence']:.3f})")
        print(f"🔧 使用方法: {result['method_used']}")
        
        if result.get('details'):
            details = result['details']
            print("📊 详细信息:")
            if 'f0_mean' in details:
                print(f"   基频: {details['f0_mean']:.1f}Hz")
            if 'spectral_centroid' in details:
                print(f"   频谱质心: {details['spectral_centroid']:.0f}Hz")
            if 'male_score' in details and 'female_score' in details:
                print(f"   评分: 男性={details['male_score']}, 女性={details['female_score']}")
    else:
        print(f"❌ 检测失败: {result.get('error', '未知错误')}")
    
    # 显示所有方法结果
    if len(result.get('all_results', [])) > 1:
        print("\n📋 所有方法结果:")
        for r in result['all_results']:
            status = "✅" if r['gender'] != '未知' else "❌"
            print(f"   {status} {r['method']}: {r['gender']} ({r['confidence']:.3f})")

if __name__ == "__main__":
    main()
