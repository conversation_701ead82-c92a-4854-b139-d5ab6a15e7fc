#!/usr/bin/env python3
"""
测试人脸识别错误修复
专门测试之前出现异常的图片
"""

import os
import requests
import time
from pathlib import Path

# API基础URL
BASE_URL = "http://localhost:9000"

def test_problematic_image(image_path: str):
    """测试之前出现问题的图片"""
    print(f"\n🔍 测试问题图片: {image_path}")
    
    if not os.path.exists(image_path):
        print(f"❌ 文件不存在: {image_path}")
        return False
    
    url = f"{BASE_URL}/face/embedding"
    
    start_time = time.time()
    try:
        with open(image_path, 'rb') as f:
            files = {'file': (os.path.basename(image_path), f, 'image/jpeg')}
            
            response = requests.post(url, files=files, timeout=30)
            processing_time = time.time() - start_time
            
            print(f"📊 响应状态码: {response.status_code}")
            print(f"⏱️  处理时间: {processing_time:.2f}s")
            
            if response.status_code == 200:
                result = response.json()
                if result['success']:
                    print(f"✅ 成功处理图片!")
                    print(f"   - 检测到人脸数量: {result['face_count']}")
                    print(f"   - 嵌入向量维度: {result['embedding_shape']}")
                    print(f"   - 向量范数: {result['embedding_norm']:.4f}")
                    print(f"   - 模型名称: {result['model_name']}")
                    print(f"   - 文件大小: {result['file_info']['size']} bytes")
                    return True
                else:
                    print(f"❌ 处理失败: {result.get('error', '未知错误')}")
                    return False
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                try:
                    error_detail = response.json()
                    print(f"   错误详情: {error_detail}")
                except:
                    print(f"   响应内容: {response.text}")
                return False
                
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")
        return False

def test_face_comparison_with_problematic_image(image1_path: str, image2_path: str):
    """测试人脸比较功能"""
    print(f"\n🔍 测试人脸比较:")
    print(f"   图片1: {image1_path}")
    print(f"   图片2: {image2_path}")
    
    if not os.path.exists(image1_path) or not os.path.exists(image2_path):
        print(f"❌ 文件不存在")
        return False
    
    url = f"{BASE_URL}/face/compare"
    
    start_time = time.time()
    try:
        with open(image1_path, 'rb') as f1, open(image2_path, 'rb') as f2:
            files = {
                'file1': (os.path.basename(image1_path), f1, 'image/jpeg'),
                'file2': (os.path.basename(image2_path), f2, 'image/jpeg')
            }
            
            response = requests.post(url, files=files, timeout=30)
            processing_time = time.time() - start_time
            
            print(f"📊 响应状态码: {response.status_code}")
            print(f"⏱️  处理时间: {processing_time:.2f}s")
            
            if response.status_code == 200:
                result = response.json()
                if result['success']:
                    print(f"✅ 人脸比较成功!")
                    print(f"   - 相似度: {result['similarity']:.4f}")
                    print(f"   - 是否同一人: {'是' if result['is_same_person'] else '否'}")
                    print(f"   - 阈值: {result['threshold']}")
                    print(f"   - 图片1人脸数: {result['face_count_1']}")
                    print(f"   - 图片2人脸数: {result['face_count_2']}")
                    return True
                else:
                    print(f"❌ 比较失败: {result.get('error', '未知错误')}")
                    return False
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                try:
                    error_detail = response.json()
                    print(f"   错误详情: {error_detail}")
                except:
                    print(f"   响应内容: {response.text}")
                return False
                
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")
        return False

def check_server():
    """检查服务器状态"""
    print("🔍 检查服务器状态...")
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 服务器运行正常")
            print(f"   - 应用: {result['app_name']}")
            print(f"   - 版本: {result['version']}")
            print(f"   - 状态: {result['status']}")
            return True
        else:
            print(f"❌ 服务器响应异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接服务器: {str(e)}")
        print("请确保服务器已启动 (python main.py)")
        return False

def find_test_images():
    """查找测试图片"""
    # 查找之前出现问题的图片
    problematic_image = "4BDA079063B9AB1C4C82B024339B2A19.jpeg"
    
    # 可能的路径
    possible_paths = [
        problematic_image,
        f"./{problematic_image}",
        f"uploads/{problematic_image}",
        f"test/{problematic_image}",
        f"test_images/{problematic_image}"
    ]
    
    found_images = []
    for path in possible_paths:
        if os.path.exists(path):
            found_images.append(path)
            print(f"✅ 找到问题图片: {path}")
    
    # 查找其他测试图片
    test_dirs = [".", "test", "test_images", "uploads"]
    image_extensions = [".jpg", ".jpeg", ".png", ".bmp", ".tiff", ".webp"]
    
    other_images = []
    for test_dir in test_dirs:
        if os.path.exists(test_dir):
            for file in os.listdir(test_dir):
                if any(file.lower().endswith(ext) for ext in image_extensions):
                    full_path = os.path.join(test_dir, file)
                    if full_path not in found_images:
                        other_images.append(full_path)
    
    return found_images, other_images[:5]  # 最多5张其他图片

def main():
    """主函数"""
    print("🚀 人脸识别错误修复测试")
    print("=" * 60)
    print("专门测试之前出现 'not enough values to unpack' 错误的图片")
    print("=" * 60)
    
    # 检查服务器
    if not check_server():
        return
    
    # 查找测试图片
    problematic_images, other_images = find_test_images()
    
    if not problematic_images and not other_images:
        print("\n⚠️  未找到测试图片")
        print("请确保以下图片存在:")
        print("   - 4BDA079063B9AB1C4C82B024339B2A19.jpeg (之前出现问题的图片)")
        print("   - 或其他任何图片文件")
        return
    
    success_count = 0
    total_count = 0
    
    # 测试之前出现问题的图片
    if problematic_images:
        print(f"\n{'='*60}")
        print("🔥 测试之前出现问题的图片")
        print(f"{'='*60}")
        
        for img_path in problematic_images:
            total_count += 1
            if test_problematic_image(img_path):
                success_count += 1
    
    # 测试其他图片
    if other_images:
        print(f"\n{'='*60}")
        print("📸 测试其他图片")
        print(f"{'='*60}")
        
        for img_path in other_images[:3]:  # 只测试前3张
            total_count += 1
            if test_problematic_image(img_path):
                success_count += 1
    
    # 测试人脸比较
    all_images = problematic_images + other_images
    if len(all_images) >= 2:
        print(f"\n{'='*60}")
        print("🔄 测试人脸比较功能")
        print(f"{'='*60}")
        
        total_count += 1
        if test_face_comparison_with_problematic_image(all_images[0], all_images[1]):
            success_count += 1
    
    # 总结
    print(f"\n{'='*60}")
    print("📊 测试结果总结")
    print(f"{'='*60}")
    print(f"总测试数: {total_count}")
    print(f"成功数: {success_count}")
    print(f"失败数: {total_count - success_count}")
    print(f"成功率: {success_count/total_count*100:.1f}%" if total_count > 0 else "无测试")
    
    if success_count == total_count:
        print("🎉 所有测试通过！错误修复成功！")
    elif success_count > 0:
        print("⚠️  部分测试通过，可能还有其他问题需要修复")
    else:
        print("❌ 所有测试失败，需要进一步检查")
    
    print(f"\n💡 修复说明:")
    print("   - 修复了 cv2.HoughLines 返回格式的兼容性问题")
    print("   - 增强了 embedding 提取的错误处理")
    print("   - 添加了更详细的日志记录")
    print("   - 提高了系统的稳定性和容错能力")

if __name__ == "__main__":
    main()
