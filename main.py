from fastapi import FastAPI, File, UploadFile, HTTPException, Form
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import os
import logging
import time
from pathlib import Path
from typing import Optional, List, Tuple
import aiofiles
import modelscope
from modelscope.pipelines import pipeline
from modelscope.utils.constant import Tasks
import numpy as np
import cv2
from insightface.app import FaceAnalysis

from config import settings
from processors.audio_processor import audio_processor
from processors.speaker_processor import speaker_processor
from models import (
    TranscriptionResponse,
    ErrorResponse,
    HealthResponse,
    LanguageResponse,
    SegmentInfo,
    SpeakerEmbeddingResponse,
    SpeakerComparisonResponse,
    ModelInfoResponse,
    BatchTranscriptionResponse,
    BatchTranscriptionItem,
    BatchSpeakerComparisonResponse,
    BatchSpeakerComparisonItem,
    BatchSpeakerEmbeddingResponse,
    BatchSpeakerEmbeddingItem,
    FaceEmbeddingResponse,
    FaceComparisonResponse,
    BatchFaceEmbeddingResponse,
    BatchFaceEmbeddingItem,
    BatchFaceComparisonResponse,
    BatchFaceComparisonItem
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

print("✅ modelscope version:", modelscope.__version__)
# 1. 加载英文/多口音说话人验证模型（VoxCeleb 数据集）
sv_pipeline = pipeline(
    task=Tasks.speaker_verification,
    # model='damo/speech_ecapa-tdnn_sv_en_voxceleb_16k'
    model='iic/speech_eres2net_large_sv_en_voxceleb_16k',
    model_revision='v1.0.0'  # 指定模型版本
)

# 2. 初始化人脸识别模型
print("🔄 正在初始化人脸识别模型...")
try:
    face_app = FaceAnalysis(
        name=settings.face_model_name,
        allowed_modules=["detection", "recognition"]
    )
    face_app.prepare(
        ctx_id=settings.face_ctx_id,
        det_size=settings.face_det_size
    )
    print("✅ 人脸识别模型初始化成功")
except Exception as e:
    print(f"❌ 人脸识别模型初始化失败: {e}")
    face_app = None

# 创建FastAPI应用
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    description="基于Whisper的音频转文字API服务"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 确保上传目录存在
os.makedirs(settings.upload_dir, exist_ok=True)

def audio_to_embedding(audio_path: str) -> np.ndarray:
    """
    将音频转为 speaker embedding
    """
    version = tuple(map(int, modelscope.__version__.split('.')))
    if version >= (1, 14, 0):
        result = sv_pipeline([audio_path], output_emb=True)
        # print(result)
        embedding = np.array(result['embs'][0])
    else:
        result = sv_pipeline(audio_in=audio_path)
        embedding = np.array(result['spk_embedding'])   
    return embedding

def validate_audio_file(file: UploadFile) -> None:
    """验证音频文件"""
    # 检查文件扩展名
    file_ext = Path(file.filename).suffix.lower()
    if file_ext not in settings.allowed_extensions:
        raise HTTPException(
            status_code=400,
            detail=f"不支持的文件格式。支持的格式: {', '.join(settings.allowed_extensions)}"
        )

    # 检查文件大小（这里只是基础检查，实际大小在上传时检查）
    if hasattr(file, 'size') and file.size > settings.max_file_size:
        raise HTTPException(
            status_code=413,
            detail=f"文件过大。最大支持 {settings.max_file_size // (1024*1024)}MB"
        )

def validate_image_file(file: UploadFile) -> None:
    """验证图片文件"""
    # 检查文件扩展名
    file_ext = Path(file.filename).suffix.lower()
    if file_ext not in settings.allowed_image_extensions:
        raise HTTPException(
            status_code=400,
            detail=f"不支持的图片格式。支持的格式: {', '.join(settings.allowed_image_extensions)}"
        )

    # 检查文件大小
    if file.size and file.size > settings.max_file_size:
        raise HTTPException(
            status_code=413,
            detail=f"文件过大。最大支持 {settings.max_file_size // (1024*1024)}MB"
        )

def l2_normalize(v: np.ndarray, eps: float = 1e-12) -> np.ndarray:
    """L2归一化"""
    return v / (np.linalg.norm(v) + eps)

def cosine_similarity(a: np.ndarray, b: np.ndarray) -> float:
    """计算余弦相似度"""
    a = l2_normalize(a)
    b = l2_normalize(b)
    return float(np.dot(a, b))

def read_image_from_bytes(image_bytes: bytes) -> np.ndarray:
    """从字节数据读取图片"""
    nparr = np.frombuffer(image_bytes, np.uint8)
    img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
    if img is None:
        raise ValueError("无法解析图片数据")
    return img

def get_face_embeddings(img: np.ndarray) -> Tuple[List[np.ndarray], int]:
    """
    获取图片中所有人脸的embedding
    返回: (embeddings列表, 人脸数量)
    """
    if face_app is None:
        raise HTTPException(
            status_code=500,
            detail="人脸识别模型未初始化"
        )

    faces = face_app.get(img)
    embeddings = []

    for face in faces:
        # 获取归一化的embedding
        emb = getattr(face, "normed_embedding", None)
        if emb is None:
            emb = face.embedding
            emb = l2_normalize(emb)
        embeddings.append(emb.astype(np.float32))

    return embeddings, len(faces)

def pick_primary_face(embeddings: List[np.ndarray]) -> np.ndarray:
    """选择主要人脸的embedding（这里选择第一个检测到的）"""
    if not embeddings:
        raise ValueError("未检测到人脸")
    return embeddings[0]

@app.get("/", response_model=HealthResponse)
async def root():
    """根路径 - 健康检查"""
    return HealthResponse(
        status="running",
        app_name=settings.app_name,
        version=settings.app_version,
        whisper_model=settings.whisper_model
    )

@app.get("/health", response_model=HealthResponse)
async def health_check():
    """健康检查接口"""
    return HealthResponse(
        status="healthy",
        app_name=settings.app_name,
        version=settings.app_version,
        whisper_model=settings.whisper_model
    )

@app.get("/languages", response_model=LanguageResponse)
async def get_supported_languages():
    """获取支持的语言列表"""
    languages = audio_processor.get_supported_languages()
    return LanguageResponse(languages=languages)

@app.get("/speaker/model-info", response_model=ModelInfoResponse)
async def get_speaker_model_info():
    """获取说话人模型信息"""
    model_info = speaker_processor.get_model_info()
    return ModelInfoResponse(**model_info)

@app.post("/transcribe", response_model=TranscriptionResponse)
async def transcribe_audio(
    file: UploadFile = File(..., description="音频文件"),
    language: Optional[str] = Form(None, description="语言代码"),
    apply_digit_mapping: bool = Form(True, description="是否应用数字映射")
):
    """
    音频转文字接口
    
    - **file**: 音频文件 (支持 wav, mp3, m4a, flac, ogg)
    - **language**: 语言代码 (可选，如 'zh', 'en')
    - **apply_digit_mapping**: 是否应用数字映射 (默认: true)
    """
    temp_file_path = None
    
    try:
        # 验证文件
        validate_audio_file(file)
        
        # 创建临时文件路径
        file_ext = Path(file.filename).suffix
        temp_file_path = os.path.join(settings.upload_dir, f"temp_{file.filename}")
        
        # 保存上传的文件
        async with aiofiles.open(temp_file_path, 'wb') as f:
            content = await file.read()
            
            # 检查实际文件大小
            if len(content) > settings.max_file_size:
                raise HTTPException(
                    status_code=413,
                    detail=f"文件过大。最大支持 {settings.max_file_size // (1024*1024)}MB"
                )
            
            await f.write(content)
        
        logger.info(f"开始处理音频文件: {file.filename}")
        
        # 执行音频转录
        result = audio_processor.transcribe_audio(
            audio_path=temp_file_path,
            language=language,
            apply_digit_mapping=apply_digit_mapping
        )
        
        # 构建响应
        segments = [
            SegmentInfo(
                id=seg.get("id", i),
                start=seg.get("start", 0.0),
                end=seg.get("end", 0.0),
                text=seg.get("text", "")
            )
            for i, seg in enumerate(result.get("segments", []))
        ]
        
        response = TranscriptionResponse(
            success=True,
            original_text=result["original_text"],
            processed_text=result["processed_text"],
            language=result["language"],
            duration=result["duration"],
            confidence=result["confidence"],
            segments=segments,
            file_info={
                "filename": file.filename,
                "size": len(content),
                "content_type": file.content_type
            }
        )
        
        logger.info(f"音频处理完成: {file.filename}")
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"处理音频文件时发生错误: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"处理音频文件时发生错误: {str(e)}"
        )
    finally:
        # 清理临时文件
        if temp_file_path and os.path.exists(temp_file_path):
            try:
                os.remove(temp_file_path)
                logger.info(f"已清理临时文件: {temp_file_path}")
            except Exception as e:
                logger.warning(f"清理临时文件失败: {str(e)}")

@app.post("/speaker/embedding", response_model=SpeakerEmbeddingResponse)
async def extract_speaker_embedding(
    file: UploadFile = File(..., description="音频文件")
):
    """
    提取说话人嵌入向量

    - **file**: 音频文件 (支持 wav, mp3, m4a, flac, ogg)
    """
    temp_file_path = None

    try:
        # 验证文件
        validate_audio_file(file)

        # 创建临时文件路径
        file_ext = Path(file.filename).suffix
        temp_file_path = os.path.join(settings.upload_dir, f"temp_speaker_{file.filename}")

        # 保存上传的文件
        async with aiofiles.open(temp_file_path, 'wb') as f:
            content = await file.read()

            # 检查实际文件大小
            if len(content) > settings.max_file_size:
                raise HTTPException(
                    status_code=413,
                    detail=f"文件过大。最大支持 {settings.max_file_size // (1024*1024)}MB"
                )

            await f.write(content)

        logger.info(f"开始提取说话人嵌入: {file.filename}")

        # 执行说话人嵌入提取
        # result = speaker_processor.extract_speaker_embedding(temp_file_path)
        emb = audio_to_embedding(temp_file_path)

        # 构建响应
        response = SpeakerEmbeddingResponse(
            success=True,
            embedding=emb.tolist(),  # 转换为列表
            embedding_shape=[len(emb)],
            embedding_norm=float(np.linalg.norm(emb)),
            model_name="file-based-features",  # 基于文件的特征
            file_info={
                "filename": file.filename,
                "size": len(content),
                "content_type": file.content_type
            }
        )

        logger.info(f"说话人嵌入提取完成: {file.filename}")
        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"提取说话人嵌入时发生错误: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"提取说话人嵌入时发生错误: {str(e)}"
        )
    finally:
        # 清理临时文件
        if temp_file_path and os.path.exists(temp_file_path):
            try:
                os.remove(temp_file_path)
                logger.info(f"已清理临时文件: {temp_file_path}")
            except Exception as e:
                logger.warning(f"清理临时文件失败: {str(e)}")

@app.post("/face/embedding", response_model=FaceEmbeddingResponse)
async def extract_face_embedding(
    file: UploadFile = File(..., description="图片文件")
):
    """
    提取人脸嵌入向量

    支持的图片格式: jpg, jpeg, png, bmp, tiff, webp
    返回检测到的第一张人脸的512维嵌入向量
    """
    start_time = time.time()

    try:
        # 验证文件
        validate_image_file(file)

        # 读取图片数据
        image_bytes = await file.read()
        img = read_image_from_bytes(image_bytes)

        # 获取人脸embeddings
        embeddings, face_count = get_face_embeddings(img)

        if face_count == 0:
            raise HTTPException(
                status_code=400,
                detail="图片中未检测到人脸"
            )

        # 选择主要人脸
        primary_embedding = pick_primary_face(embeddings)

        # 计算向量范数
        embedding_norm = float(np.linalg.norm(primary_embedding))

        # 文件信息
        file_info = {
            "filename": file.filename,
            "size": len(image_bytes),
            "processing_time": time.time() - start_time
        }

        return FaceEmbeddingResponse(
            success=True,
            embedding=primary_embedding.tolist(),
            embedding_shape=list(primary_embedding.shape),
            embedding_norm=embedding_norm,
            face_count=face_count,
            model_name=settings.face_model_name,
            file_info=file_info
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"人脸嵌入提取失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"人脸嵌入提取失败: {str(e)}"
        )

@app.post("/face/compare", response_model=FaceComparisonResponse)
async def compare_faces(
    file1: UploadFile = File(..., description="第一张图片"),
    file2: UploadFile = File(..., description="第二张图片")
):
    """
    比较两张图片中的人脸相似度

    支持的图片格式: jpg, jpeg, png, bmp, tiff, webp
    返回人脸相似度分数和是否为同一人的判断
    """
    start_time = time.time()

    try:
        # 验证文件
        validate_image_file(file1)
        validate_image_file(file2)

        # 读取图片数据
        image1_bytes = await file1.read()
        image2_bytes = await file2.read()

        img1 = read_image_from_bytes(image1_bytes)
        img2 = read_image_from_bytes(image2_bytes)

        # 获取人脸embeddings
        embeddings1, face_count1 = get_face_embeddings(img1)
        embeddings2, face_count2 = get_face_embeddings(img2)

        if face_count1 == 0:
            raise HTTPException(
                status_code=400,
                detail="第一张图片中未检测到人脸"
            )

        if face_count2 == 0:
            raise HTTPException(
                status_code=400,
                detail="第二张图片中未检测到人脸"
            )

        # 选择主要人脸
        emb1 = pick_primary_face(embeddings1)
        emb2 = pick_primary_face(embeddings2)

        # 计算相似度
        similarity = cosine_similarity(emb1, emb2)
        is_same_person = similarity >= settings.face_similarity_threshold

        # 文件信息
        files_info = {
            "file1": file1.filename,
            "file2": file2.filename,
            "processing_time": time.time() - start_time
        }

        return FaceComparisonResponse(
            success=True,
            similarity=similarity,
            is_same_person=is_same_person,
            threshold=settings.face_similarity_threshold,
            model_name=settings.face_model_name,
            face_count_1=face_count1,
            face_count_2=face_count2,
            files=files_info
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"人脸比较失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"人脸比较失败: {str(e)}"
        )

@app.post("/speaker/compare", response_model=SpeakerComparisonResponse)
async def compare_speakers(
    file1: UploadFile = File(..., description="第一个音频文件"),
    file2: UploadFile = File(..., description="第二个音频文件")
):
    """
    比较两个音频的说话人相似度

    - **file1**: 第一个音频文件 (支持 wav, mp3, m4a, flac, ogg)
    - **file2**: 第二个音频文件 (支持 wav, mp3, m4a, flac, ogg)
    """
    temp_file_path1 = None
    temp_file_path2 = None

    try:
        # 验证文件
        validate_audio_file(file1)
        validate_audio_file(file2)

        # 创建临时文件路径
        file_ext1 = Path(file1.filename).suffix
        file_ext2 = Path(file2.filename).suffix
        temp_file_path1 = os.path.join(settings.upload_dir, f"temp_compare1_{file1.filename}")
        temp_file_path2 = os.path.join(settings.upload_dir, f"temp_compare2_{file2.filename}")

        # 保存第一个文件
        async with aiofiles.open(temp_file_path1, 'wb') as f:
            content1 = await file1.read()
            if len(content1) > settings.max_file_size:
                raise HTTPException(
                    status_code=413,
                    detail=f"文件1过大。最大支持 {settings.max_file_size // (1024*1024)}MB"
                )
            await f.write(content1)

        # 保存第二个文件
        async with aiofiles.open(temp_file_path2, 'wb') as f:
            content2 = await file2.read()
            if len(content2) > settings.max_file_size:
                raise HTTPException(
                    status_code=413,
                    detail=f"文件2过大。最大支持 {settings.max_file_size // (1024*1024)}MB"
                )
            await f.write(content2)

        logger.info(f"开始比较说话人: {file1.filename} vs {file2.filename}")

        # 执行说话人比较
        result = speaker_processor.compare_speakers(temp_file_path1, temp_file_path2)

        # 构建响应
        response = SpeakerComparisonResponse(
            success=True,
            similarity=result["similarity"],
            is_same_speaker=result["is_same_speaker"],
            threshold=result["threshold"],
            model_name=result["model_name"],
            files={
                "file1": file1.filename,
                "file2": file2.filename
            }
        )

        logger.info(f"说话人比较完成: {file1.filename} vs {file2.filename}, 相似度: {result['similarity']:.4f}")
        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"比较说话人时发生错误: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"比较说话人时发生错误: {str(e)}"
        )
    finally:
        # 清理临时文件
        for temp_path in [temp_file_path1, temp_file_path2]:
            if temp_path and os.path.exists(temp_path):
                try:
                    os.remove(temp_path)
                    logger.info(f"已清理临时文件: {temp_path}")
                except Exception as e:
                    logger.warning(f"清理临时文件失败: {str(e)}")

@app.post("/batch/transcribe", response_model=BatchTranscriptionResponse)
async def batch_transcribe(
    files: List[UploadFile] = File(..., description="音频文件列表"),
    language: str = Form("auto", description="语言代码"),
    response_format: str = Form("json", description="响应格式"),
    apply_digit_mapping: bool = Form(True, description="是否应用数字映射")
):
    """
    批量音频转录

    - **files**: 音频文件列表 (支持 wav, mp3, m4a, flac, ogg)
    - **language**: 语言代码 (auto, zh, en, ja, ko 等)
    - **response_format**: 响应格式 (json, text, verbose_json)
    - **apply_digit_mapping**: 是否将文字数字转换为阿拉伯数字
    """
    start_time = time.time()
    temp_files = []
    results = []

    try:
        logger.info(f"开始批量处理 {len(files)} 个音频文件")

        for file in files:
            result_item = BatchTranscriptionItem(
                filename=file.filename,
                success=False
            )

            temp_file_path = None
            try:
                # 验证文件
                validate_audio_file(file)

                # 创建临时文件路径
                file_ext = Path(file.filename).suffix
                temp_file_path = os.path.join(settings.upload_dir, f"batch_temp_{file.filename}")
                temp_files.append(temp_file_path)

                # 保存上传的文件
                async with aiofiles.open(temp_file_path, 'wb') as f:
                    content = await file.read()

                    # 检查实际文件大小
                    if len(content) > settings.max_file_size:
                        raise HTTPException(
                            status_code=413,
                            detail=f"文件 {file.filename} 过大。最大支持 {settings.max_file_size // (1024*1024)}MB"
                        )

                    await f.write(content)

                logger.info(f"开始处理音频文件: {file.filename}")

                # 执行转录
                transcription_result = audio_processor.transcribe_audio(
                    temp_file_path,
                    language=language,
                    response_format=response_format
                )

                # 更新结果
                result_item.success = True
                result_item.text = transcription_result["original_text"]
                result_item.processed_text = transcription_result["processed_text"]
                result_item.language = transcription_result["language"]
                result_item.duration = transcription_result.get("duration", 0)
                result_item.confidence = transcription_result.get("confidence", 0)

                logger.info(f"音频处理完成: {file.filename}")

            except HTTPException as e:
                result_item.error = e.detail
                logger.error(f"处理文件 {file.filename} 时发生HTTP错误: {e.detail}")
            except Exception as e:
                result_item.error = str(e)
                logger.error(f"处理文件 {file.filename} 时发生错误: {str(e)}")
            finally:
                # 清理临时文件
                if temp_file_path and os.path.exists(temp_file_path):
                    try:
                        os.remove(temp_file_path)
                        logger.info(f"已清理临时文件: {temp_file_path}")
                    except Exception as e:
                        logger.warning(f"清理临时文件失败: {str(e)}")

            results.append(result_item)

        # 统计结果
        successful_files = sum(1 for r in results if r.success)
        failed_files = len(results) - successful_files
        total_duration = time.time() - start_time

        response = BatchTranscriptionResponse(
            success=failed_files == 0,
            total_files=len(files),
            successful_files=successful_files,
            failed_files=failed_files,
            results=results,
            total_duration=total_duration
        )

        logger.info(f"批量处理完成: {successful_files}/{len(files)} 成功, 耗时: {total_duration:.2f}秒")
        return response

    except Exception as e:
        logger.error(f"批量转录时发生错误: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"批量转录时发生错误: {str(e)}"
        )
    finally:
        # 清理所有临时文件
        for temp_path in temp_files:
            if os.path.exists(temp_path):
                try:
                    os.remove(temp_path)
                except Exception as e:
                    logger.warning(f"清理临时文件失败: {str(e)}")

@app.post("/batch/face/embedding", response_model=BatchFaceEmbeddingResponse)
async def batch_face_embedding(
    files: List[UploadFile] = File(..., description="图片文件列表")
):
    """
    批量人脸嵌入提取

    支持的图片格式: jpg, jpeg, png, bmp, tiff, webp
    对每张图片提取第一张检测到的人脸的512维嵌入向量
    """
    start_time = time.time()
    results = []
    successful_files = 0
    failed_files = 0

    logger.info(f"开始批量人脸嵌入提取，共 {len(files)} 个文件")

    for file in files:
        file_start_time = time.time()
        try:
            # 验证文件
            validate_image_file(file)

            # 读取图片数据
            image_bytes = await file.read()
            img = read_image_from_bytes(image_bytes)

            # 获取人脸embeddings
            embeddings, face_count = get_face_embeddings(img)

            if face_count == 0:
                results.append(BatchFaceEmbeddingItem(
                    filename=file.filename,
                    success=False,
                    error="图片中未检测到人脸"
                ))
                failed_files += 1
                continue

            # 选择主要人脸
            primary_embedding = pick_primary_face(embeddings)
            embedding_norm = float(np.linalg.norm(primary_embedding))

            results.append(BatchFaceEmbeddingItem(
                filename=file.filename,
                success=True,
                embedding=primary_embedding.tolist(),
                embedding_shape=list(primary_embedding.shape),
                embedding_norm=embedding_norm,
                face_count=face_count
            ))
            successful_files += 1

            logger.info(f"✅ {file.filename} 处理成功，耗时 {time.time() - file_start_time:.2f}s")

        except Exception as e:
            error_msg = str(e)
            logger.error(f"❌ {file.filename} 处理失败: {error_msg}")

            results.append(BatchFaceEmbeddingItem(
                filename=file.filename,
                success=False,
                error=error_msg
            ))
            failed_files += 1

    total_duration = time.time() - start_time
    logger.info(f"批量人脸嵌入提取完成，成功: {successful_files}, 失败: {failed_files}, 总耗时: {total_duration:.2f}s")

    return BatchFaceEmbeddingResponse(
        success=successful_files > 0,
        total_files=len(files),
        successful_files=successful_files,
        failed_files=failed_files,
        results=results,
        model_name=settings.face_model_name,
        total_duration=total_duration
    )

@app.post("/batch/face/compare", response_model=BatchFaceComparisonResponse)
async def batch_face_compare(
    files: List[UploadFile] = File(..., description="图片文件列表（至少2个文件）")
):
    """
    批量人脸比较 - 对所有文件进行两两比较

    支持的图片格式: jpg, jpeg, png, bmp, tiff, webp
    返回所有图片之间的人脸相似度比较结果
    """
    start_time = time.time()

    if len(files) < 2:
        raise HTTPException(
            status_code=400,
            detail="至少需要2个文件进行比较"
        )

    logger.info(f"开始批量人脸比较，共 {len(files)} 个文件")

    # 首先提取所有文件的embeddings
    file_embeddings = {}
    file_face_counts = {}

    for file in files:
        try:
            validate_image_file(file)
            image_bytes = await file.read()
            img = read_image_from_bytes(image_bytes)
            embeddings, face_count = get_face_embeddings(img)

            if face_count > 0:
                file_embeddings[file.filename] = pick_primary_face(embeddings)
                file_face_counts[file.filename] = face_count
            else:
                file_face_counts[file.filename] = 0

        except Exception as e:
            logger.error(f"处理文件 {file.filename} 失败: {str(e)}")
            file_face_counts[file.filename] = 0

    # 进行两两比较
    results = []
    successful_comparisons = 0
    failed_comparisons = 0

    filenames = [f.filename for f in files]
    for i in range(len(filenames)):
        for j in range(i + 1, len(filenames)):
            file1_name = filenames[i]
            file2_name = filenames[j]

            try:
                if file1_name not in file_embeddings:
                    raise ValueError(f"{file1_name} 中未检测到人脸")
                if file2_name not in file_embeddings:
                    raise ValueError(f"{file2_name} 中未检测到人脸")

                emb1 = file_embeddings[file1_name]
                emb2 = file_embeddings[file2_name]

                similarity = cosine_similarity(emb1, emb2)
                is_same_person = similarity >= settings.face_similarity_threshold

                results.append(BatchFaceComparisonItem(
                    file1=file1_name,
                    file2=file2_name,
                    success=True,
                    similarity=similarity,
                    is_same_person=is_same_person,
                    face_count_1=file_face_counts[file1_name],
                    face_count_2=file_face_counts[file2_name]
                ))
                successful_comparisons += 1

            except Exception as e:
                error_msg = str(e)
                logger.error(f"比较 {file1_name} 和 {file2_name} 失败: {error_msg}")

                results.append(BatchFaceComparisonItem(
                    file1=file1_name,
                    file2=file2_name,
                    success=False,
                    face_count_1=file_face_counts.get(file1_name, 0),
                    face_count_2=file_face_counts.get(file2_name, 0),
                    error=error_msg
                ))
                failed_comparisons += 1

    total_duration = time.time() - start_time
    total_comparisons = len(results)

    logger.info(f"批量人脸比较完成，成功: {successful_comparisons}, 失败: {failed_comparisons}, 总耗时: {total_duration:.2f}s")

    return BatchFaceComparisonResponse(
        success=successful_comparisons > 0,
        total_comparisons=total_comparisons,
        successful_comparisons=successful_comparisons,
        failed_comparisons=failed_comparisons,
        results=results,
        threshold=settings.face_similarity_threshold,
        model_name=settings.face_model_name
    )

@app.post("/batch/speaker/embedding", response_model=BatchSpeakerEmbeddingResponse)
async def batch_speaker_embedding(
    files: List[UploadFile] = File(..., description="音频文件列表")
):
    """
    批量说话人嵌入提取

    - **files**: 音频文件列表 (支持 wav, mp3, m4a, flac, ogg)

    返回每个文件的说话人嵌入向量
    """
    start_time = time.time()
    temp_files = []
    results = []

    try:
        logger.info(f"开始批量提取说话人嵌入: {len(files)} 个文件")

        for i, file in enumerate(files):
            result_item = BatchSpeakerEmbeddingItem(
                filename=file.filename,
                success=False
            )

            temp_file_path = None
            try:
                # 验证文件
                validate_audio_file(file)

                # 创建临时文件路径
                temp_file_path = os.path.join(settings.upload_dir, f"batch_embedding_{i}_{file.filename}")
                temp_files.append(temp_file_path)

                # 保存上传的文件
                async with aiofiles.open(temp_file_path, 'wb') as f:
                    content = await file.read()

                    if len(content) > settings.max_file_size:
                        raise HTTPException(
                            status_code=413,
                            detail=f"文件 {file.filename} 过大。最大支持 {settings.max_file_size // (1024*1024)}MB"
                        )

                    await f.write(content)

                logger.info(f"开始提取说话人嵌入: {file.filename}")

                # 执行说话人嵌入提取
                embedding_result = speaker_processor.extract_speaker_embedding(temp_file_path)

                result_item.success = True
                result_item.embedding = embedding_result["embedding"]
                result_item.embedding_shape = embedding_result["embedding_shape"]
                result_item.embedding_norm = embedding_result["embedding_norm"]

                logger.info(f"说话人嵌入提取完成: {file.filename}")

            except HTTPException as e:
                result_item.error = e.detail
                logger.error(f"处理文件 {file.filename} 时发生HTTP错误: {e.detail}")
            except Exception as e:
                result_item.error = str(e)
                logger.error(f"处理文件 {file.filename} 时发生错误: {str(e)}")
            finally:
                # 清理临时文件
                if temp_file_path and os.path.exists(temp_file_path):
                    try:
                        os.remove(temp_file_path)
                        logger.info(f"已清理临时文件: {temp_file_path}")
                    except Exception as e:
                        logger.warning(f"清理临时文件失败: {str(e)}")

            results.append(result_item)

        # 统计结果
        successful_files = sum(1 for r in results if r.success)
        failed_files = len(results) - successful_files
        total_duration = time.time() - start_time

        response = BatchSpeakerEmbeddingResponse(
            success=failed_files == 0,
            total_files=len(files),
            successful_files=successful_files,
            failed_files=failed_files,
            results=results,
            model_name=speaker_processor.model_name,
            total_duration=total_duration
        )

        logger.info(f"批量说话人嵌入提取完成: {successful_files}/{len(files)} 成功, 耗时: {total_duration:.2f}秒")
        return response

    except Exception as e:
        logger.error(f"批量说话人嵌入提取时发生错误: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"批量说话人嵌入提取时发生错误: {str(e)}"
        )
    finally:
        # 清理所有临时文件
        for temp_path in temp_files:
            if os.path.exists(temp_path):
                try:
                    os.remove(temp_path)
                except Exception as e:
                    logger.warning(f"清理临时文件失败: {str(e)}")

@app.post("/batch/speaker/compare", response_model=BatchSpeakerComparisonResponse)
async def batch_speaker_compare(
    files: List[UploadFile] = File(..., description="音频文件列表（至少2个文件）")
):
    """
    批量说话人比较 - 对所有文件进行两两比较

    - **files**: 音频文件列表 (至少2个文件，支持 wav, mp3, m4a, flac, ogg)

    返回所有文件的两两比较结果
    """
    if len(files) < 2:
        raise HTTPException(
            status_code=400,
            detail="至少需要2个音频文件进行比较"
        )

    temp_files = []
    results = []

    try:
        logger.info(f"开始批量说话人比较: {len(files)} 个文件")

        # 保存所有文件
        file_paths = {}
        for i, file in enumerate(files):
            try:
                # 验证文件
                validate_audio_file(file)

                # 创建临时文件路径
                temp_file_path = os.path.join(settings.upload_dir, f"batch_speaker_{i}_{file.filename}")
                temp_files.append(temp_file_path)

                # 保存上传的文件
                async with aiofiles.open(temp_file_path, 'wb') as f:
                    content = await file.read()

                    if len(content) > settings.max_file_size:
                        raise HTTPException(
                            status_code=413,
                            detail=f"文件 {file.filename} 过大。最大支持 {settings.max_file_size // (1024*1024)}MB"
                        )

                    await f.write(content)

                file_paths[file.filename] = temp_file_path
                logger.info(f"已保存文件: {file.filename}")

            except Exception as e:
                logger.error(f"保存文件 {file.filename} 时发生错误: {str(e)}")
                raise HTTPException(
                    status_code=400,
                    detail=f"保存文件 {file.filename} 时发生错误: {str(e)}"
                )

        # 进行两两比较
        file_names = list(file_paths.keys())
        total_comparisons = 0
        successful_comparisons = 0

        for i in range(len(file_names)):
            for j in range(i + 1, len(file_names)):
                file1_name = file_names[i]
                file2_name = file_names[j]
                file1_path = file_paths[file1_name]
                file2_path = file_paths[file2_name]

                total_comparisons += 1

                result_item = BatchSpeakerComparisonItem(
                    file1=file1_name,
                    file2=file2_name,
                    success=False
                )

                try:
                    logger.info(f"比较说话人: {file1_name} vs {file2_name}")

                    # 执行说话人比较
                    comparison_result = speaker_processor.compare_speakers(file1_path, file2_path)

                    result_item.success = True
                    result_item.similarity = comparison_result["similarity"]
                    result_item.is_same_speaker = comparison_result["is_same_speaker"]
                    successful_comparisons += 1

                    logger.info(f"比较完成: {file1_name} vs {file2_name}, 相似度: {comparison_result['similarity']:.4f}")

                except Exception as e:
                    result_item.error = str(e)
                    logger.error(f"比较 {file1_name} vs {file2_name} 时发生错误: {str(e)}")

                results.append(result_item)

        # 构建响应
        response = BatchSpeakerComparisonResponse(
            success=successful_comparisons == total_comparisons,
            total_comparisons=total_comparisons,
            successful_comparisons=successful_comparisons,
            failed_comparisons=total_comparisons - successful_comparisons,
            results=results,
            threshold=speaker_processor.threshold,
            model_name=speaker_processor.model_name
        )

        logger.info(f"批量说话人比较完成: {successful_comparisons}/{total_comparisons} 成功")
        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"批量说话人比较时发生错误: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"批量说话人比较时发生错误: {str(e)}"
        )
    finally:
        # 清理所有临时文件
        for temp_path in temp_files:
            if os.path.exists(temp_path):
                try:
                    os.remove(temp_path)
                    logger.info(f"已清理临时文件: {temp_path}")
                except Exception as e:
                    logger.warning(f"清理临时文件失败: {str(e)}")

@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """全局异常处理器"""
    logger.error(f"未处理的异常: {str(exc)}")
    return JSONResponse(
        status_code=500,
        content=ErrorResponse(
            error="服务器内部错误",
            error_code="INTERNAL_ERROR"
        ).model_dump()
    )

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug
    )
