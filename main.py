from fastapi import FastAPI, File, UploadFile, HTTPException, Form
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import os
import logging
import time
import math
from pathlib import Path
from typing import Optional, List, Tuple
import aiofiles
import modelscope
from modelscope.pipelines import pipeline
from modelscope.utils.constant import Tasks
import numpy as np
import cv2
from insightface.app import FaceAnalysis

from config import settings
from processors.audio_processor import audio_processor
from processors.speaker_processor import speaker_processor
from models import (
    TranscriptionResponse,
    ErrorResponse,
    HealthResponse,
    LanguageResponse,
    SegmentInfo,
    SpeakerEmbeddingResponse,
    SpeakerComparisonResponse,
    ModelInfoResponse,
    BatchTranscriptionResponse,
    BatchTranscriptionItem,
    BatchSpeakerComparisonResponse,
    BatchSpeakerComparisonItem,
    BatchSpeakerEmbeddingResponse,
    BatchSpeakerEmbeddingItem,
    FaceEmbeddingResponse,
    FaceComparisonResponse,
    BatchFaceEmbeddingResponse,
    BatchFaceEmbeddingItem,
    BatchFaceComparisonResponse,
    BatchFaceComparisonItem
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

print("✅ modelscope version:", modelscope.__version__)
# 1. 加载英文/多口音说话人验证模型（VoxCeleb 数据集）
sv_pipeline = pipeline(
    task=Tasks.speaker_verification,
    # model='damo/speech_ecapa-tdnn_sv_en_voxceleb_16k'
    model='iic/speech_eres2net_large_sv_en_voxceleb_16k',
    model_revision='v1.0.0'  # 指定模型版本
)

# 2. 初始化人脸识别模型
print("🔄 正在初始化人脸识别模型...")
try:
    face_app = FaceAnalysis(
        name=settings.face_model_name,
        allowed_modules=["detection", "recognition"]
    )
    face_app.prepare(
        ctx_id=settings.face_ctx_id,
        det_size=settings.face_det_size
    )
    print("✅ 人脸识别模型初始化成功")
except Exception as e:
    print(f"❌ 人脸识别模型初始化失败: {e}")
    face_app = None

# 创建FastAPI应用
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    description="基于Whisper的音频转文字API服务"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 确保上传目录存在
os.makedirs(settings.upload_dir, exist_ok=True)

def audio_to_embedding(audio_path: str) -> np.ndarray:
    """
    将音频转为 speaker embedding
    """
    version = tuple(map(int, modelscope.__version__.split('.')))
    if version >= (1, 14, 0):
        result = sv_pipeline([audio_path], output_emb=True)
        # print(result)
        embedding = np.array(result['embs'][0])
    else:
        result = sv_pipeline(audio_in=audio_path)
        embedding = np.array(result['spk_embedding'])   
    return embedding

def validate_audio_file(file: UploadFile) -> None:
    """验证音频文件"""
    # 检查文件扩展名
    file_ext = Path(file.filename).suffix.lower()
    if file_ext not in settings.allowed_extensions:
        raise HTTPException(
            status_code=400,
            detail=f"不支持的文件格式。支持的格式: {', '.join(settings.allowed_extensions)}"
        )

    # 检查文件大小（这里只是基础检查，实际大小在上传时检查）
    if hasattr(file, 'size') and file.size > settings.max_file_size:
        raise HTTPException(
            status_code=413,
            detail=f"文件过大。最大支持 {settings.max_file_size // (1024*1024)}MB"
        )

def validate_image_file(file: UploadFile) -> None:
    """验证图片文件"""
    # 检查文件扩展名
    file_ext = Path(file.filename).suffix.lower()
    if file_ext not in settings.allowed_image_extensions:
        raise HTTPException(
            status_code=400,
            detail=f"不支持的图片格式。支持的格式: {', '.join(settings.allowed_image_extensions)}"
        )

    # 检查文件大小
    if file.size and file.size > settings.max_file_size:
        raise HTTPException(
            status_code=413,
            detail=f"文件过大。最大支持 {settings.max_file_size // (1024*1024)}MB"
        )

def l2_normalize(v: np.ndarray, eps: float = 1e-12) -> np.ndarray:
    """L2归一化"""
    return v / (np.linalg.norm(v) + eps)

def cosine_similarity(a: np.ndarray, b: np.ndarray) -> float:
    """计算余弦相似度"""
    a = l2_normalize(a)
    b = l2_normalize(b)
    return float(np.dot(a, b))

def read_image_from_bytes(image_bytes: bytes) -> np.ndarray:
    """从字节数据读取图片"""
    nparr = np.frombuffer(image_bytes, np.uint8)
    img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
    if img is None:
        raise ValueError("无法解析图片数据")
    return img

def detect_text_orientation(img: np.ndarray) -> float:
    """
    使用文本检测来判断图片的方向
    返回需要旋转的角度（0, 90, 180, 270）
    """
    try:
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

        # 使用边缘检测来找到文本区域
        edges = cv2.Canny(gray, 50, 150, apertureSize=3)

        # 使用霍夫变换检测直线
        lines = cv2.HoughLines(edges, 1, np.pi/180, threshold=100)

        if lines is not None and len(lines) > 0:
            angles = []
            for line in lines[:20]:  # 只取前20条线
                try:
                    # 正确解包霍夫变换结果: lines的形状是(n, 1, 2)
                    rho, theta = line[0]
                    angle = theta * 180 / np.pi
                    # 将角度标准化到0-180度
                    if angle > 90:
                        angle = angle - 180
                    angles.append(angle)
                except (IndexError, ValueError, TypeError) as e:
                    logger.warning(f"解包霍夫变换结果失败: {e}, line shape: {line.shape if hasattr(line, 'shape') else 'unknown'}")
                    continue

            if angles:
                # 计算主要角度
                median_angle = np.median(angles)

                # 判断需要旋转的角度
                if -45 <= median_angle <= 45:
                    return 0  # 不需要旋转
                elif 45 < median_angle <= 135:
                    return 270  # 逆时针90度
                elif median_angle > 135 or median_angle < -135:
                    return 180  # 180度
                else:  # -135 <= median_angle < -45
                    return 90   # 顺时针90度

        logger.debug("霍夫变换未检测到有效线条，使用默认方向")
        return 0  # 默认不旋转
        
    except Exception as e:
        logger.warning(f"文本方向检测失败: {e}")
        return 0  # 出错时默认不旋转

def detect_face_orientation(img: np.ndarray) -> float:
    """
    通过人脸检测来判断图片方向
    返回最佳的旋转角度
    """
    if face_app is None:
        return 0

    best_angle = 0
    max_faces = 0
    max_confidence = 0

    for angle in [0, 90, 180, 270]:
        test_img = rotate_image(img, angle)
        faces = face_app.get(test_img)

        if len(faces) > max_faces:
            max_faces = len(faces)
            best_angle = angle
            max_confidence = max([f.det_score for f in faces]) if faces else 0
        elif len(faces) == max_faces and faces:
            # 如果人脸数量相同，选择置信度更高的
            current_confidence = max([f.det_score for f in faces])
            if current_confidence > max_confidence:
                max_confidence = current_confidence
                best_angle = angle

    return best_angle

def rotate_image(img: np.ndarray, angle: float) -> np.ndarray:
    """
    旋转图像指定角度
    angle: 旋转角度 (90, 180, 270)
    """
    if angle == 90:
        return cv2.rotate(img, cv2.ROTATE_90_CLOCKWISE)
    elif angle == 180:
        return cv2.rotate(img, cv2.ROTATE_180)
    elif angle == 270:
        return cv2.rotate(img, cv2.ROTATE_90_COUNTERCLOCKWISE)
    else:
        return img

def auto_correct_orientation(img: np.ndarray) -> Tuple[np.ndarray, float]:
    """
    自动矫正图片方向
    返回: (矫正后的图片, 旋转角度)
    """
    logger.info("🔍 分析图片方向...")

    try:
        # 方法1: 先尝试人脸检测
        logger.info("  📱 使用人脸检测分析方向...")
        face_angle = detect_face_orientation(img)

        if face_angle != 0:
            logger.info(f"  ✅ 人脸检测建议旋转: {face_angle}度")
            corrected_img = rotate_image(img, face_angle)
            return corrected_img, face_angle

        # 方法2: 如果人脸检测无效，使用文本方向检测
        logger.info("  📝 使用文本方向分析...")
        text_angle = detect_text_orientation(img)

        if text_angle != 0:
            logger.info(f"  ✅ 文本分析建议旋转: {text_angle}度")
            corrected_img = rotate_image(img, text_angle)
            return corrected_img, text_angle

        logger.info("  ℹ️  图片方向正常，无需矫正")
        return img, 0
        
    except Exception as e:
        logger.error(f"自动方向矫正失败: {e}")
        logger.info("  ⚠️  方向矫正失败，返回原图")
        return img, 0

def get_face_embeddings_with_auto_correction(img: np.ndarray) -> Tuple[List[np.ndarray], int, List]:
    """
    使用自动矫正和多种预处理技术进行人脸检测
    返回: (embeddings列表, 人脸数量, faces对象列表)
    """
    if face_app is None:
        raise HTTPException(
            status_code=500,
            detail="人脸识别模型未初始化"
        )

    try:

        # 1. 首先尝试原图
        faces = face_app.get(img)
        if len(faces) > 0:
            logger.info(f"✅ 原图检测到 {len(faces)} 张人脸")
            embeddings = []
            for i, f in enumerate(faces):
                emb = safe_extract_embedding(f, i+1)
                if emb is not None:
                    embeddings.append(emb)
                    logger.debug(f"人脸{i+1}: embedding形状={emb.shape}")

            if embeddings:
                logger.info(f"最终返回{len(embeddings)}个有效embeddings")
                return embeddings, len(faces), faces
            else:
                logger.warning("虽然检测到人脸，但无法提取有效embedding")

    # 2. 尝试预处理版本
    logger.info("⚠️  原图未检测到人脸，尝试图像增强...")
    processed_images = preprocess_image_for_face_detection(img)

        for i, processed_img in enumerate(processed_images[1:], 1):  # 跳过原图
            faces = face_app.get(processed_img)
            if len(faces) > 0:
                logger.info(f"✅ 预处理版本{i}检测到 {len(faces)} 张人脸")
                embeddings = []
                for j, f in enumerate(faces):
                    emb = safe_extract_embedding(f, j+1)
                    if emb is not None:
                        embeddings.append(emb)
                if embeddings:
                    return embeddings, len(faces), faces

    # 3. 如果预处理无效，使用自动方向矫正
    logger.info("⚠️  预处理无效，启动自动方向矫正...")
    corrected_img, rotation_angle = auto_correct_orientation(img)

    if rotation_angle != 0:
            # 在矫正后的图片上检测人脸
            faces = face_app.get(corrected_img)
            if len(faces) > 0:
                logger.info(f"✅ 方向矫正后检测到 {len(faces)} 张人脸")
                embeddings = []
                for j, f in enumerate(faces):
                    emb = safe_extract_embedding(f, j+1)
                    if emb is not None:
                        embeddings.append(emb)
                if embeddings:
                    return embeddings, len(faces), faces

        # 4. 对矫正后的图片也尝试预处理
        logger.info("⚠️  方向矫正后仍未检测到，尝试矫正+增强...")
        processed_corrected = preprocess_image_for_face_detection(corrected_img)

            for i, processed_img in enumerate(processed_corrected[1:], 1):
                faces = face_app.get(processed_img)
                if len(faces) > 0:
                    logger.info(f"✅ 矫正+增强版本{i}检测到 {len(faces)} 张人脸")
                    embeddings = []
                    for j, f in enumerate(faces):
                        emb = safe_extract_embedding(f, j+1)
                        if emb is not None:
                            embeddings.append(emb)
                    if embeddings:
                        return embeddings, len(faces), faces

    # 5. 最后尝试降低检测阈值
    logger.info("⚠️  尝试降低检测阈值...")
    try:
        # 临时降低检测阈值
        original_det_thresh = getattr(face_app.det_model, 'det_thresh', 0.5)
        face_app.det_model.det_thresh = 0.3
        
            faces = face_app.get(img)
            if len(faces) > 0:
                logger.info(f"✅ 降低阈值后检测到 {len(faces)} 张人脸")
                embeddings = []
                for j, f in enumerate(faces):
                    emb = safe_extract_embedding(f, j+1)
                    if emb is not None:
                        embeddings.append(emb)
                # 恢复原阈值
                face_app.det_model.det_thresh = original_det_thresh
                if embeddings:
                    return embeddings, len(faces), faces
        
        # 恢复原阈值
        face_app.det_model.det_thresh = original_det_thresh
        
    except Exception as e:
        logger.warning(f"降低检测阈值失败: {e}")

        logger.info("❌ 所有方法尝试后仍未检测到人脸")
        return [], 0, []

    except Exception as e:
        logger.error(f"人脸检测过程中发生异常: {str(e)}")
        logger.error("错误堆栈:", exc_info=True)
        return [], 0, []

def get_face_embeddings(img: np.ndarray) -> Tuple[List[np.ndarray], int]:
    """
    获取图片中所有人脸的embedding，使用智能自动矫正功能
    返回: (embeddings列表, 人脸数量)
    """
    embeddings, count, _ = get_face_embeddings_with_auto_correction(img)
    return embeddings, count

def get_face_embeddings_with_faces(img: np.ndarray) -> Tuple[List[np.ndarray], int, List]:
    """
    获取图片中所有人脸的embedding和faces对象，使用智能自动矫正功能
    返回: (embeddings列表, 人脸数量, faces对象列表)
    """
    try:
        return get_face_embeddings_with_auto_correction(img)
    except Exception as e:
        logger.error(f"获取人脸embeddings失败: {str(e)}")
        logger.error("错误堆栈:", exc_info=True)
        return [], 0, []

def safe_extract_embedding(face, face_index: int = 0) -> Optional[np.ndarray]:
    """
    安全地从face对象中提取embedding
    """
    try:
        # 尝试获取归一化的embedding
        emb = getattr(face, "normed_embedding", None)
        if emb is not None:
            logger.debug(f"人脸{face_index}: 使用normed_embedding")
            return emb.astype(np.float32)

        # 如果没有归一化的embedding，使用原始embedding
        emb = getattr(face, "embedding", None)
        if emb is not None:
            logger.debug(f"人脸{face_index}: 使用原始embedding并进行L2归一化")
            emb = l2_normalize(emb)
            return emb.astype(np.float32)

        logger.warning(f"人脸{face_index}: 无法获取embedding")
        return None

    except Exception as e:
        logger.error(f"人脸{face_index}: 提取embedding失败: {str(e)}")
        return None

def calculate_face_quality(face) -> float:
    """
    计算人脸质量分数
    综合考虑检测置信度、人脸大小、清晰度等因素
    """
    logger.debug(f"计算人脸质量，face对象类型: {type(face)}")

    # 检查face对象是否为None
    if face is None:
        logger.error("calculate_face_quality: face对象为None")
        return 0.0

    # 基础检测置信度
    quality_score = getattr(face, 'det_score', 0.5)
    logger.debug(f"检测置信度: {quality_score}")

    # 人脸框大小（更大的人脸通常质量更好）
    bbox = getattr(face, 'bbox', None)
    if bbox is not None:
        face_area = (bbox[2] - bbox[0]) * (bbox[3] - bbox[1])
        area_score = min(face_area / 10000, 1.0)  # 归一化到0-1
        logger.debug(f"人脸面积分数: {area_score}")
    else:
        area_score = 0.5
        logger.warning("人脸bbox信息缺失，使用默认面积分数")

    # 人脸角度（正面人脸质量更好）
    if hasattr(face, 'pose') and face.pose is not None:
        pose = face.pose
        try:
            # 计算偏离正面的角度
            angle_penalty = abs(pose[0]) + abs(pose[1]) + abs(pose[2])
            angle_score = max(0, 1 - angle_penalty / 90)  # 角度越小分数越高
        except (TypeError, IndexError) as e:
            logger.warning(f"计算人脸角度时出错: {e}, pose={pose}")
            angle_score = 1.0
    else:
        angle_score = 1.0

    # 综合质量分数
    final_score = quality_score * 0.5 + area_score * 0.3 + angle_score * 0.2
    logger.debug(f"最终质量分数: {final_score} (检测:{quality_score}, 面积:{area_score}, 角度:{angle_score})")
    return final_score

def pick_primary_face(embeddings: List[np.ndarray], faces: List = None) -> np.ndarray:
    """
    选择主要人脸的embedding
    如果提供了faces信息，会根据质量分数选择最佳人脸
    否则选择第一个检测到的人脸
    """
    logger.info(f"pick_primary_face 调用: embeddings数量={len(embeddings) if embeddings else 0}, faces数量={len(faces) if faces else 0}")

    if not embeddings:
        logger.error("embeddings列表为空")
        raise ValueError("未检测到人脸")

    # 检查embeddings中是否有None值
    for i, emb in enumerate(embeddings):
        if emb is None:
            logger.error(f"embeddings[{i}] 为 None")
            raise ValueError(f"第{i}个embedding为None")

    if faces and len(faces) == len(embeddings):
        # 根据质量分数选择最佳人脸
        logger.info("使用质量评估选择最佳人脸")

        # 检查faces中是否有None值
        for i, face in enumerate(faces):
            if face is None:
                logger.error(f"faces[{i}] 为 None")

        try:
            quality_scores = [calculate_face_quality(face) for face in faces]
            logger.info(f"所有人脸质量分数: {quality_scores}")
            best_index = np.argmax(quality_scores)
            logger.info(f"选择质量最佳的人脸 (索引: {best_index}, 质量分数: {quality_scores[best_index]:.3f})")
            selected_embedding = embeddings[best_index]
            logger.info(f"选中的embedding形状: {selected_embedding.shape if selected_embedding is not None else 'None'}")
            return selected_embedding
        except Exception as e:
            logger.error(f"质量评估过程中出错: {e}")
            logger.info("回退到使用默认方式选择第一个人脸")
            selected_embedding = embeddings[0]
            logger.info(f"选中的embedding形状: {selected_embedding.shape if selected_embedding is not None else 'None'}")
            return selected_embedding
    else:
        # 默认选择第一个人脸
        logger.info("使用默认方式选择第一个人脸")
        selected_embedding = embeddings[0]
        logger.info(f"选中的embedding形状: {selected_embedding.shape if selected_embedding is not None else 'None'}")
        return selected_embedding

def enhance_image_quality(img: np.ndarray) -> np.ndarray:
    """
    增强图片质量以提高人脸检测效果
    """
    # 1. 直方图均衡化增强对比度
    lab = cv2.cvtColor(img, cv2.COLOR_BGR2LAB)
    lab[:,:,0] = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8)).apply(lab[:,:,0])
    enhanced = cv2.cvtColor(lab, cv2.COLOR_LAB2BGR)

    # 2. 轻微的锐化
    kernel = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
    sharpened = cv2.filter2D(enhanced, -1, kernel)

    # 3. 混合原图和增强图
    result = cv2.addWeighted(enhanced, 0.7, sharpened, 0.3, 0)

    return result

def preprocess_image_for_face_detection(img: np.ndarray) -> List[np.ndarray]:
    """
    对图像进行多种预处理以提高人脸检测成功率
    返回预处理后的图像列表
    """
    processed_images = [img]  # 原图
    
    try:
        # 1. 调整亮度和对比度
        alpha = 1.2  # 对比度
        beta = 30    # 亮度
        bright_img = cv2.convertScaleAbs(img, alpha=alpha, beta=beta)
        processed_images.append(bright_img)
        
        # 2. 直方图均衡化
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        equalized = cv2.equalizeHist(gray)
        equalized_bgr = cv2.cvtColor(equalized, cv2.COLOR_GRAY2BGR)
        processed_images.append(equalized_bgr)
        
        # 3. 高斯模糊去噪
        blurred = cv2.GaussianBlur(img, (3, 3), 0)
        processed_images.append(blurred)
        
        # 4. 锐化
        kernel = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
        sharpened = cv2.filter2D(img, -1, kernel)
        processed_images.append(sharpened)
        
        # 5. 缩放到不同尺寸
        h, w = img.shape[:2]
        if min(h, w) > 640:
            # 缩小图像
            scale = 640 / min(h, w)
            new_w, new_h = int(w * scale), int(h * scale)
            resized = cv2.resize(img, (new_w, new_h))
            processed_images.append(resized)
        
        if min(h, w) < 300:
            # 放大图像
            scale = 300 / min(h, w)
            new_w, new_h = int(w * scale), int(h * scale)
            enlarged = cv2.resize(img, (new_w, new_h))
            processed_images.append(enlarged)
            
    except Exception as e:
        logger.warning(f"图像预处理失败: {e}")
    
    return processed_images

@app.get("/", response_model=HealthResponse)
async def root():
    """根路径 - 健康检查"""
    return HealthResponse(
        status="running",
        app_name=settings.app_name,
        version=settings.app_version,
        whisper_model=settings.whisper_model
    )

@app.get("/health", response_model=HealthResponse)
async def health_check():
    """健康检查接口"""
    return HealthResponse(
        status="healthy",
        app_name=settings.app_name,
        version=settings.app_version,
        whisper_model=settings.whisper_model
    )

@app.get("/languages", response_model=LanguageResponse)
async def get_supported_languages():
    """获取支持的语言列表"""
    languages = audio_processor.get_supported_languages()
    return LanguageResponse(languages=languages)

@app.get("/speaker/model-info", response_model=ModelInfoResponse)
async def get_speaker_model_info():
    """获取说话人模型信息"""
    model_info = speaker_processor.get_model_info()
    return ModelInfoResponse(**model_info)

@app.post("/transcribe", response_model=TranscriptionResponse)
async def transcribe_audio(
    file: UploadFile = File(..., description="音频文件"),
    language: Optional[str] = Form(None, description="语言代码"),
    apply_digit_mapping: bool = Form(True, description="是否应用数字映射")
):
    """
    音频转文字接口
    
    - **file**: 音频文件 (支持 wav, mp3, m4a, flac, ogg)
    - **language**: 语言代码 (可选，如 'zh', 'en')
    - **apply_digit_mapping**: 是否应用数字映射 (默认: true)
    """
    temp_file_path = None
    
    try:
        # 验证文件
        validate_audio_file(file)
        
        # 创建临时文件路径
        file_ext = Path(file.filename).suffix
        temp_file_path = os.path.join(settings.upload_dir, f"temp_{file.filename}")
        
        # 保存上传的文件
        async with aiofiles.open(temp_file_path, 'wb') as f:
            content = await file.read()
            
            # 检查实际文件大小
            if len(content) > settings.max_file_size:
                raise HTTPException(
                    status_code=413,
                    detail=f"文件过大。最大支持 {settings.max_file_size // (1024*1024)}MB"
                )
            
            await f.write(content)
        
        logger.info(f"开始处理音频文件: {file.filename}")
        
        # 执行音频转录
        result = audio_processor.transcribe_audio(
            audio_path=temp_file_path,
            language=language,
            apply_digit_mapping=apply_digit_mapping
        )
        
        # 构建响应
        segments = [
            SegmentInfo(
                id=seg.get("id", i),
                start=seg.get("start", 0.0),
                end=seg.get("end", 0.0),
                text=seg.get("text", "")
            )
            for i, seg in enumerate(result.get("segments", []))
        ]
        
        response = TranscriptionResponse(
            success=True,
            original_text=result["original_text"],
            processed_text=result["processed_text"],
            language=result["language"],
            duration=result["duration"],
            confidence=result["confidence"],
            segments=segments,
            file_info={
                "filename": file.filename,
                "size": len(content),
                "content_type": file.content_type
            }
        )

        logger.info(f"音频处理完成: {file.filename}")
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"处理音频文件时发生错误: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"处理音频文件时发生错误: {str(e)}"
        )
    finally:
        # 清理临时文件
        if temp_file_path and os.path.exists(temp_file_path):
            try:
                os.remove(temp_file_path)
                logger.info(f"已清理临时文件: {temp_file_path}")
            except Exception as e:
                logger.warning(f"清理临时文件失败: {str(e)}")

@app.post("/speaker/embedding", response_model=SpeakerEmbeddingResponse)
async def extract_speaker_embedding(
    file: UploadFile = File(..., description="音频文件")
):
    """
    提取说话人嵌入向量

    - **file**: 音频文件 (支持 wav, mp3, m4a, flac, ogg)
    """
    temp_file_path = None

    try:
        # 验证文件
        validate_audio_file(file)

        # 创建临时文件路径
        file_ext = Path(file.filename).suffix
        temp_file_path = os.path.join(settings.upload_dir, f"temp_speaker_{file.filename}")

        # 保存上传的文件
        async with aiofiles.open(temp_file_path, 'wb') as f:
            content = await file.read()

            # 检查实际文件大小
            if len(content) > settings.max_file_size:
                raise HTTPException(
                    status_code=413,
                    detail=f"文件过大。最大支持 {settings.max_file_size // (1024*1024)}MB"
                )

            await f.write(content)

        logger.info(f"开始提取说话人嵌入: {file.filename}")

        # 执行说话人嵌入提取
        # result = speaker_processor.extract_speaker_embedding(temp_file_path)
        emb = audio_to_embedding(temp_file_path)

        # 构建响应
        response = SpeakerEmbeddingResponse(
            success=True,
            embedding=emb.tolist(),  # 转换为列表
            embedding_shape=[len(emb)],
            embedding_norm=float(np.linalg.norm(emb)),
            model_name="file-based-features",  # 基于文件的特征
            file_info={
                "filename": file.filename,
                "size": len(content),
                "content_type": file.content_type
            }
        )

        logger.info(f"说话人嵌入提取完成: {file.filename}")
        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"提取说话人嵌入时发生错误: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"提取说话人嵌入时发生错误: {str(e)}"
        )
    finally:
        # 清理临时文件
        if temp_file_path and os.path.exists(temp_file_path):
            try:
                os.remove(temp_file_path)
                logger.info(f"已清理临时文件: {temp_file_path}")
            except Exception as e:
                logger.warning(f"清理临时文件失败: {str(e)}")

@app.post("/face/embedding", response_model=FaceEmbeddingResponse)
async def extract_face_embedding(
    file: UploadFile = File(..., description="图片文件")
):
    """
    提取人脸嵌入向量

    支持的图片格式: jpg, jpeg, png, bmp, tiff, webp
    返回检测到的第一张人脸的512维嵌入向量
    """
    start_time = time.time()

    try:
        # 验证文件
        validate_image_file(file)

        # 读取图片数据
        image_bytes = await file.read()
        img = read_image_from_bytes(image_bytes)

        # 获取人脸embeddings和faces对象
        logger.info("开始提取人脸embeddings...")
        embeddings, face_count, faces = get_face_embeddings_with_faces(img)
        logger.info(f"提取结果: face_count={face_count}, embeddings数量={len(embeddings) if embeddings else 0}")

        if face_count == 0:
            logger.warning("未检测到人脸，返回400错误")
            raise HTTPException(
                status_code=400,
                detail="图片中未检测到人脸"
            )

        # 选择主要人脸（使用质量评估）
        logger.info("开始选择主要人脸...")
        primary_embedding = pick_primary_face(embeddings, faces)
        logger.info(f"主要人脸选择完成，embedding形状: {primary_embedding.shape if primary_embedding is not None else 'None'}")

        # 计算向量范数
        embedding_norm = float(np.linalg.norm(primary_embedding))

        # 文件信息
        file_info = {
            "filename": file.filename,
            "size": len(image_bytes),
            "processing_time": time.time() - start_time
        }

        return FaceEmbeddingResponse(
            success=True,
            embedding=primary_embedding.tolist(),
            embedding_shape=list(primary_embedding.shape),
            embedding_norm=embedding_norm,
            face_count=face_count,
            model_name=settings.face_model_name,
            file_info=file_info
        )

    except HTTPException:
        raise
    except Exception as e:
        import traceback
        error_traceback = traceback.format_exc()
        logger.error(f"人脸嵌入提取失败: {str(e)}")
        logger.error(f"错误堆栈:\n{error_traceback}")
        raise HTTPException(
            status_code=500,
            detail=f"人脸嵌入提取失败: {str(e)}"
        )

@app.post("/face/compare", response_model=FaceComparisonResponse)
async def compare_faces(
    file1: UploadFile = File(..., description="第一张图片"),
    file2: UploadFile = File(..., description="第二张图片")
):
    """
    比较两张图片中的人脸相似度

    支持的图片格式: jpg, jpeg, png, bmp, tiff, webp
    返回人脸相似度分数和是否为同一人的判断
    """
    start_time = time.time()

    try:
        # 验证文件
        validate_image_file(file1)
        validate_image_file(file2)

        # 读取图片数据
        image1_bytes = await file1.read()
        image2_bytes = await file2.read()

        img1 = read_image_from_bytes(image1_bytes)
        img2 = read_image_from_bytes(image2_bytes)

        # 获取人脸embeddings和faces对象
        embeddings1, face_count1, faces1 = get_face_embeddings_with_faces(img1)
        embeddings2, face_count2, faces2 = get_face_embeddings_with_faces(img2)

        if face_count1 == 0:
            raise HTTPException(
                status_code=400,
                detail="第一张图片中未检测到人脸"
            )

        if face_count2 == 0:
            raise HTTPException(
                status_code=400,
                detail="第二张图片中未检测到人脸"
            )

        # 选择主要人脸（使用质量评估）
        emb1 = pick_primary_face(embeddings1, faces1)
        emb2 = pick_primary_face(embeddings2, faces2)

        # 计算相似度
        similarity = cosine_similarity(emb1, emb2)
        is_same_person = similarity >= settings.face_similarity_threshold

        # 文件信息
        files_info = {
            "file1": file1.filename,
            "file2": file2.filename,
            "processing_time": time.time() - start_time
        }

        return FaceComparisonResponse(
            success=True,
            similarity=similarity,
            is_same_person=is_same_person,
            threshold=settings.face_similarity_threshold,
            model_name=settings.face_model_name,
            face_count_1=face_count1,
            face_count_2=face_count2,
            files=files_info
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"人脸比较失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"人脸比较失败: {str(e)}"
        )

@app.post("/speaker/compare", response_model=SpeakerComparisonResponse)
async def compare_speakers(
    file1: UploadFile = File(..., description="第一个音频文件"),
    file2: UploadFile = File(..., description="第二个音频文件")
):
    """
    比较两个音频的说话人相似度

    - **file1**: 第一个音频文件 (支持 wav, mp3, m4a, flac, ogg)
    - **file2**: 第二个音频文件 (支持 wav, mp3, m4a, flac, ogg)
    """
    temp_file_path1 = None
    temp_file_path2 = None

    try:
        # 验证文件
        validate_audio_file(file1)
        validate_audio_file(file2)

        # 创建临时文件路径
        file_ext1 = Path(file1.filename).suffix
        file_ext2 = Path(file2.filename).suffix
        temp_file_path1 = os.path.join(settings.upload_dir, f"temp_compare1_{file1.filename}")
        temp_file_path2 = os.path.join(settings.upload_dir, f"temp_compare2_{file2.filename}")

        # 保存第一个文件
        async with aiofiles.open(temp_file_path1, 'wb') as f:
            content1 = await file1.read()
            if len(content1) > settings.max_file_size:
                raise HTTPException(
                    status_code=413,
                    detail=f"文件1过大。最大支持 {settings.max_file_size // (1024*1024)}MB"
                )
            await f.write(content1)

        # 保存第二个文件
        async with aiofiles.open(temp_file_path2, 'wb') as f:
            content2 = await file2.read()
            if len(content2) > settings.max_file_size:
                raise HTTPException(
                    status_code=413,
                    detail=f"文件2过大。最大支持 {settings.max_file_size // (1024*1024)}MB"
                )
            await f.write(content2)

        logger.info(f"开始比较说话人: {file1.filename} vs {file2.filename}")

        # 执行说话人比较
        result = speaker_processor.compare_speakers(temp_file_path1, temp_file_path2)

        # 构建响应
        response = SpeakerComparisonResponse(
            success=True,
            similarity=result["similarity"],
            is_same_speaker=result["is_same_speaker"],
            threshold=result["threshold"],
            model_name=result["model_name"],
            files={
                "file1": file1.filename,
                "file2": file2.filename
            }
        )

        logger.info(f"说话人比较完成: {file1.filename} vs {file2.filename}, 相似度: {result['similarity']:.4f}")
        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"比较说话人时发生错误: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"比较说话人时发生错误: {str(e)}"
        )
    finally:
        # 清理临时文件
        for temp_path in [temp_file_path1, temp_file_path2]:
            if temp_path and os.path.exists(temp_path):
                try:
                    os.remove(temp_path)
                    logger.info(f"已清理临时文件: {temp_path}")
                except Exception as e:
                    logger.warning(f"清理临时文件失败: {str(e)}")

@app.post("/batch/transcribe", response_model=BatchTranscriptionResponse)
async def batch_transcribe(
    files: List[UploadFile] = File(..., description="音频文件列表"),
    language: str = Form("auto", description="语言代码"),
    response_format: str = Form("json", description="响应格式"),
    apply_digit_mapping: bool = Form(True, description="是否应用数字映射")
):
    """
    批量音频转录

    - **files**: 音频文件列表 (支持 wav, mp3, m4a, flac, ogg)
    - **language**: 语言代码 (auto, zh, en, ja, ko 等)
    - **response_format**: 响应格式 (json, text, verbose_json)
    - **apply_digit_mapping**: 是否将文字数字转换为阿拉伯数字
    """
    start_time = time.time()
    temp_files = []
    results = []

    try:
        logger.info(f"开始批量处理 {len(files)} 个音频文件")

        for file in files:
            result_item = BatchTranscriptionItem(
                filename=file.filename,
                success=False
            )

            temp_file_path = None
            try:
                # 验证文件
                validate_audio_file(file)

                # 创建临时文件路径
                file_ext = Path(file.filename).suffix
                temp_file_path = os.path.join(settings.upload_dir, f"batch_temp_{file.filename}")
                temp_files.append(temp_file_path)

                # 保存上传的文件
                async with aiofiles.open(temp_file_path, 'wb') as f:
                    content = await file.read()

                    # 检查实际文件大小
                    if len(content) > settings.max_file_size:
                        raise HTTPException(
                            status_code=413,
                            detail=f"文件 {file.filename} 过大。最大支持 {settings.max_file_size // (1024*1024)}MB"
                        )

                    await f.write(content)

                logger.info(f"开始处理音频文件: {file.filename}")

                # 执行转录
                transcription_result = audio_processor.transcribe_audio(
                    temp_file_path,
                    language=language,
                    response_format=response_format
                )

                # 更新结果
                result_item.success = True
                result_item.text = transcription_result["original_text"]
                result_item.processed_text = transcription_result["processed_text"]
                result_item.language = transcription_result["language"]
                result_item.duration = transcription_result.get("duration", 0)
                result_item.confidence = transcription_result.get("confidence", 0)

                logger.info(f"音频处理完成: {file.filename}")

            except HTTPException as e:
                result_item.error = e.detail
                logger.error(f"处理文件 {file.filename} 时发生HTTP错误: {e.detail}")
            except Exception as e:
                result_item.error = str(e)
                logger.error(f"处理文件 {file.filename} 时发生错误: {str(e)}")
            finally:
                # 清理临时文件
                if temp_file_path and os.path.exists(temp_file_path):
                    try:
                        os.remove(temp_file_path)
                        logger.info(f"已清理临时文件: {temp_file_path}")
                    except Exception as e:
                        logger.warning(f"清理临时文件失败: {str(e)}")

            results.append(result_item)

        # 统计结果
        successful_files = sum(1 for r in results if r.success)
        failed_files = len(results) - successful_files
        total_duration = time.time() - start_time

        response = BatchTranscriptionResponse(
            success=failed_files == 0,
            total_files=len(files),
            successful_files=successful_files,
            failed_files=failed_files,
            results=results,
            total_duration=total_duration
        )

        logger.info(f"批量处理完成: {successful_files}/{len(files)} 成功, 耗时: {total_duration:.2f}秒")
        return response

    except Exception as e:
        logger.error(f"批量转录时发生错误: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"批量转录时发生错误: {str(e)}"
        )
    finally:
        # 清理所有临时文件
        for temp_path in temp_files:
            if os.path.exists(temp_path):
                try:
                    os.remove(temp_path)
                except Exception as e:
                    logger.warning(f"清理临时文件失败: {str(e)}")

@app.post("/batch/face/embedding", response_model=BatchFaceEmbeddingResponse)
async def batch_face_embedding(
    files: List[UploadFile] = File(..., description="图片文件列表")
):
    """
    批量人脸嵌入提取

    支持的图片格式: jpg, jpeg, png, bmp, tiff, webp
    对每张图片提取第一张检测到的人脸的512维嵌入向量
    """
    start_time = time.time()
    results = []
    successful_files = 0
    failed_files = 0

    logger.info(f"开始批量人脸嵌入提取，共 {len(files)} 个文件")

    for file in files:
        file_start_time = time.time()
        try:
            # 验证文件
            validate_image_file(file)

            # 读取图片数据
            image_bytes = await file.read()
            img = read_image_from_bytes(image_bytes)

            # 获取人脸embeddings和faces对象
            embeddings, face_count, faces = get_face_embeddings_with_faces(img)

            if face_count == 0:
                results.append(BatchFaceEmbeddingItem(
                    filename=file.filename,
                    success=False,
                    error="图片中未检测到人脸"
                ))
                failed_files += 1
                continue

            # 选择主要人脸（使用质量评估）
            primary_embedding = pick_primary_face(embeddings, faces)
            embedding_norm = float(np.linalg.norm(primary_embedding))

            results.append(BatchFaceEmbeddingItem(
                filename=file.filename,
                success=True,
                embedding=primary_embedding.tolist(),
                embedding_shape=list(primary_embedding.shape),
                embedding_norm=embedding_norm,
                face_count=face_count
            ))
            successful_files += 1

            logger.info(f"✅ {file.filename} 处理成功，耗时 {time.time() - file_start_time:.2f}s")

        except Exception as e:
            error_msg = str(e)
            logger.error(f"❌ {file.filename} 处理失败: {error_msg}")

            results.append(BatchFaceEmbeddingItem(
                filename=file.filename,
                success=False,
                error=error_msg
            ))
            failed_files += 1

    total_duration = time.time() - start_time
    logger.info(f"批量人脸嵌入提取完成，成功: {successful_files}, 失败: {failed_files}, 总耗时: {total_duration:.2f}s")

    return BatchFaceEmbeddingResponse(
        success=successful_files > 0,
        total_files=len(files),
        successful_files=successful_files,
        failed_files=failed_files,
        results=results,
        model_name=settings.face_model_name,
        total_duration=total_duration
    )

@app.post("/batch/face/compare", response_model=BatchFaceComparisonResponse)
async def batch_face_compare(
    files: List[UploadFile] = File(..., description="图片文件列表（至少2个文件）")
):
    """
    批量人脸比较 - 对所有文件进行两两比较

    支持的图片格式: jpg, jpeg, png, bmp, tiff, webp
    返回所有图片之间的人脸相似度比较结果
    """
    start_time = time.time()

    if len(files) < 2:
        raise HTTPException(
            status_code=400,
            detail="至少需要2个文件进行比较"
        )

    logger.info(f"开始批量人脸比较，共 {len(files)} 个文件")

    # 首先提取所有文件的embeddings
    file_embeddings = {}
    file_face_counts = {}

    for file in files:
        try:
            validate_image_file(file)
            image_bytes = await file.read()
            img = read_image_from_bytes(image_bytes)
            embeddings, face_count, faces = get_face_embeddings_with_faces(img)

            if face_count > 0:
                file_embeddings[file.filename] = pick_primary_face(embeddings, faces)
                file_face_counts[file.filename] = face_count
            else:
                file_face_counts[file.filename] = 0

        except Exception as e:
            logger.error(f"处理文件 {file.filename} 失败: {str(e)}")
            file_face_counts[file.filename] = 0

    # 进行两两比较
    results = []
    successful_comparisons = 0
    failed_comparisons = 0

    filenames = [f.filename for f in files]
    for i in range(len(filenames)):
        for j in range(i + 1, len(filenames)):
            file1_name = filenames[i]
            file2_name = filenames[j]

            try:
                if file1_name not in file_embeddings:
                    raise ValueError(f"{file1_name} 中未检测到人脸")
                if file2_name not in file_embeddings:
                    raise ValueError(f"{file2_name} 中未检测到人脸")

                emb1 = file_embeddings[file1_name]
                emb2 = file_embeddings[file2_name]

                similarity = cosine_similarity(emb1, emb2)
                is_same_person = similarity >= settings.face_similarity_threshold

                results.append(BatchFaceComparisonItem(
                    file1=file1_name,
                    file2=file2_name,
                    success=True,
                    similarity=similarity,
                    is_same_person=is_same_person,
                    face_count_1=file_face_counts[file1_name],
                    face_count_2=file_face_counts[file2_name]
                ))
                successful_comparisons += 1

            except Exception as e:
                error_msg = str(e)
                logger.error(f"比较 {file1_name} 和 {file2_name} 失败: {error_msg}")

                results.append(BatchFaceComparisonItem(
                    file1=file1_name,
                    file2=file2_name,
                    success=False,
                    face_count_1=file_face_counts.get(file1_name, 0),
                    face_count_2=file_face_counts.get(file2_name, 0),
                    error=error_msg
                ))
                failed_comparisons += 1

    total_duration = time.time() - start_time
    total_comparisons = len(results)

    logger.info(f"批量人脸比较完成，成功: {successful_comparisons}, 失败: {failed_comparisons}, 总耗时: {total_duration:.2f}s")

    return BatchFaceComparisonResponse(
        success=successful_comparisons > 0,
        total_comparisons=total_comparisons,
        successful_comparisons=successful_comparisons,
        failed_comparisons=failed_comparisons,
        results=results,
        threshold=settings.face_similarity_threshold,
        model_name=settings.face_model_name
    )

@app.post("/batch/speaker/embedding", response_model=BatchSpeakerEmbeddingResponse)
async def batch_speaker_embedding(
    files: List[UploadFile] = File(..., description="音频文件列表")
):
    """
    批量说话人嵌入提取

    - **files**: 音频文件列表 (支持 wav, mp3, m4a, flac, ogg)

    返回每个文件的说话人嵌入向量
    """
    start_time = time.time()
    temp_files = []
    results = []

    try:
        logger.info(f"开始批量提取说话人嵌入: {len(files)} 个文件")

        for i, file in enumerate(files):
            result_item = BatchSpeakerEmbeddingItem(
                filename=file.filename,
                success=False
            )

            temp_file_path = None
            try:
                # 验证文件
                validate_audio_file(file)

                # 创建临时文件路径
                temp_file_path = os.path.join(settings.upload_dir, f"batch_embedding_{i}_{file.filename}")
                temp_files.append(temp_file_path)

                # 保存上传的文件
                async with aiofiles.open(temp_file_path, 'wb') as f:
                    content = await file.read()

                    if len(content) > settings.max_file_size:
                        raise HTTPException(
                            status_code=413,
                            detail=f"文件 {file.filename} 过大。最大支持 {settings.max_file_size // (1024*1024)}MB"
                        )

                    await f.write(content)

                logger.info(f"开始提取说话人嵌入: {file.filename}")

                # 执行说话人嵌入提取
                embedding_result = speaker_processor.extract_speaker_embedding(temp_file_path)

                result_item.success = True
                result_item.embedding = embedding_result["embedding"]
                result_item.embedding_shape = embedding_result["embedding_shape"]
                result_item.embedding_norm = embedding_result["embedding_norm"]

                logger.info(f"说话人嵌入提取完成: {file.filename}")

            except HTTPException as e:
                result_item.error = e.detail
                logger.error(f"处理文件 {file.filename} 时发生HTTP错误: {e.detail}")
            except Exception as e:
                result_item.error = str(e)
                logger.error(f"处理文件 {file.filename} 时发生错误: {str(e)}")
            finally:
                # 清理临时文件
                if temp_file_path and os.path.exists(temp_file_path):
                    try:
                        os.remove(temp_file_path)
                        logger.info(f"已清理临时文件: {temp_file_path}")
                    except Exception as e:
                        logger.warning(f"清理临时文件失败: {str(e)}")

            results.append(result_item)

        # 统计结果
        successful_files = sum(1 for r in results if r.success)
        failed_files = len(results) - successful_files
        total_duration = time.time() - start_time

        response = BatchSpeakerEmbeddingResponse(
            success=failed_files == 0,
            total_files=len(files),
            successful_files=successful_files,
            failed_files=failed_files,
            results=results,
            model_name=speaker_processor.model_name,
            total_duration=total_duration
        )

        logger.info(f"批量说话人嵌入提取完成: {successful_files}/{len(files)} 成功, 耗时: {total_duration:.2f}秒")
        return response

    except Exception as e:
        logger.error(f"批量说话人嵌入提取时发生错误: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"批量说话人嵌入提取时发生错误: {str(e)}"
        )
    finally:
        # 清理所有临时文件
        for temp_path in temp_files:
            if os.path.exists(temp_path):
                try:
                    os.remove(temp_path)
                except Exception as e:
                    logger.warning(f"清理临时文件失败: {str(e)}")

@app.post("/batch/speaker/compare", response_model=BatchSpeakerComparisonResponse)
async def batch_speaker_compare(
    files: List[UploadFile] = File(..., description="音频文件列表（至少2个文件）")
):
    """
    批量说话人比较 - 对所有文件进行两两比较

    - **files**: 音频文件列表 (至少2个文件，支持 wav, mp3, m4a, flac, ogg)

    返回所有文件的两两比较结果
    """
    if len(files) < 2:
        raise HTTPException(
            status_code=400,
            detail="至少需要2个音频文件进行比较"
        )

    temp_files = []
    results = []

    try:
        logger.info(f"开始批量说话人比较: {len(files)} 个文件")

        # 保存所有文件
        file_paths = {}
        for i, file in enumerate(files):
            try:
                # 验证文件
                validate_audio_file(file)

                # 创建临时文件路径
                temp_file_path = os.path.join(settings.upload_dir, f"batch_speaker_{i}_{file.filename}")
                temp_files.append(temp_file_path)

                # 保存上传的文件
                async with aiofiles.open(temp_file_path, 'wb') as f:
                    content = await file.read()

                    if len(content) > settings.max_file_size:
                        raise HTTPException(
                            status_code=413,
                            detail=f"文件 {file.filename} 过大。最大支持 {settings.max_file_size // (1024*1024)}MB"
                        )

                    await f.write(content)

                file_paths[file.filename] = temp_file_path
                logger.info(f"已保存文件: {file.filename}")

            except Exception as e:
                logger.error(f"保存文件 {file.filename} 时发生错误: {str(e)}")
                raise HTTPException(
                    status_code=400,
                    detail=f"保存文件 {file.filename} 时发生错误: {str(e)}"
                )

        # 进行两两比较
        file_names = list(file_paths.keys())
        total_comparisons = 0
        successful_comparisons = 0

        for i in range(len(file_names)):
            for j in range(i + 1, len(file_names)):
                file1_name = file_names[i]
                file2_name = file_names[j]
                file1_path = file_paths[file1_name]
                file2_path = file_paths[file2_name]

                total_comparisons += 1

                result_item = BatchSpeakerComparisonItem(
                    file1=file1_name,
                    file2=file2_name,
                    success=False
                )

                try:
                    logger.info(f"比较说话人: {file1_name} vs {file2_name}")

                    # 执行说话人比较
                    comparison_result = speaker_processor.compare_speakers(file1_path, file2_path)

                    result_item.success = True
                    result_item.similarity = comparison_result["similarity"]
                    result_item.is_same_speaker = comparison_result["is_same_speaker"]
                    successful_comparisons += 1

                    logger.info(f"比较完成: {file1_name} vs {file2_name}, 相似度: {comparison_result['similarity']:.4f}")

                except Exception as e:
                    result_item.error = str(e)
                    logger.error(f"比较 {file1_name} vs {file2_name} 时发生错误: {str(e)}")

                results.append(result_item)

        # 构建响应
        response = BatchSpeakerComparisonResponse(
            success=successful_comparisons == total_comparisons,
            total_comparisons=total_comparisons,
            successful_comparisons=successful_comparisons,
            failed_comparisons=total_comparisons - successful_comparisons,
            results=results,
            threshold=speaker_processor.threshold,
            model_name=speaker_processor.model_name
        )

        logger.info(f"批量说话人比较完成: {successful_comparisons}/{total_comparisons} 成功")
        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"批量说话人比较时发生错误: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"批量说话人比较时发生错误: {str(e)}"
        )
    finally:
        # 清理所有临时文件
        for temp_path in temp_files:
            if os.path.exists(temp_path):
                try:
                    os.remove(temp_path)
                    logger.info(f"已清理临时文件: {temp_path}")
                except Exception as e:
                    logger.warning(f"清理临时文件失败: {str(e)}")

@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """全局异常处理器"""
    logger.error(f"未处理的异常: {str(exc)}")
    return JSONResponse(
        status_code=500,
        content=ErrorResponse(
            error="服务器内部错误",
            error_code="INTERNAL_ERROR"
        ).model_dump()
    )

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug
    )
