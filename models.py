from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any

class TranscriptionRequest(BaseModel):
    """转录请求模型"""
    language: Optional[str] = Field(None, description="语言代码，如 'zh', 'en'")
    apply_digit_mapping: bool = Field(True, description="是否应用数字映射")

class SegmentInfo(BaseModel):
    """音频片段信息"""
    id: int = Field(..., description="片段ID")
    start: float = Field(..., description="开始时间（秒）")
    end: float = Field(..., description="结束时间（秒）")
    text: str = Field(..., description="片段文本")

class TranscriptionResponse(BaseModel):
    """转录响应模型"""
    success: bool = Field(..., description="是否成功")
    original_text: str = Field(..., description="原始识别文本")
    processed_text: str = Field(..., description="处理后文本")
    language: str = Field(..., description="检测到的语言")
    duration: float = Field(..., description="音频时长（秒）")
    confidence: float = Field(..., description="平均置信度")
    segments: List[SegmentInfo] = Field(default=[], description="音频片段信息")
    file_info: Dict[str, Any] = Field(..., description="文件信息")

class ErrorResponse(BaseModel):
    """错误响应模型"""
    success: bool = Field(False, description="是否成功")
    error: str = Field(..., description="错误信息")
    error_code: str = Field(..., description="错误代码")

class HealthResponse(BaseModel):
    """健康检查响应"""
    status: str = Field(..., description="服务状态")
    app_name: str = Field(..., description="应用名称")
    version: str = Field(..., description="版本号")
    whisper_model: str = Field(..., description="Whisper模型")

class LanguageResponse(BaseModel):
    """支持语言响应"""
    languages: Dict[str, str] = Field(..., description="支持的语言列表")

class SpeakerEmbeddingResponse(BaseModel):
    """说话人嵌入响应"""
    success: bool = Field(..., description="是否成功")
    embedding: List[float] = Field(..., description="说话人嵌入向量")
    embedding_shape: List[int] = Field(..., description="嵌入向量形状")
    embedding_norm: float = Field(..., description="嵌入向量范数")
    model_name: str = Field(..., description="使用的模型名称")
    file_info: Dict[str, Any] = Field(..., description="文件信息")

class SpeakerComparisonRequest(BaseModel):
    """说话人比较请求模型"""
    pass  # 文件通过form-data上传

class SpeakerComparisonResponse(BaseModel):
    """说话人比较响应"""
    success: bool = Field(..., description="是否成功")
    similarity: float = Field(..., description="说话人相似度")
    is_same_speaker: bool = Field(..., description="是否同一个说话人")
    threshold: float = Field(..., description="相似度阈值")
    model_name: str = Field(..., description="使用的模型名称")
    files: Dict[str, str] = Field(..., description="文件信息")

class ModelInfoResponse(BaseModel):
    """模型信息响应"""
    model_name: str = Field(..., description="模型名称")
    task: str = Field(..., description="任务类型")
    dataset: str = Field(..., description="训练数据集")
    language: str = Field(..., description="支持语言")
    loaded: bool = Field(..., description="是否已加载")
    threshold: float = Field(..., description="相似度阈值")

class BatchTranscriptionItem(BaseModel):
    """批量转录单项结果"""
    filename: str = Field(..., description="文件名")
    success: bool = Field(..., description="是否成功")
    text: Optional[str] = Field(None, description="转录文本")
    processed_text: Optional[str] = Field(None, description="处理后文本")
    language: Optional[str] = Field(None, description="检测到的语言")
    duration: Optional[float] = Field(None, description="音频时长")
    confidence: Optional[float] = Field(None, description="平均置信度")
    error: Optional[str] = Field(None, description="错误信息")

class BatchTranscriptionResponse(BaseModel):
    """批量转录响应"""
    success: bool = Field(..., description="整体是否成功")
    total_files: int = Field(..., description="总文件数")
    successful_files: int = Field(..., description="成功处理的文件数")
    failed_files: int = Field(..., description="失败的文件数")
    results: List[BatchTranscriptionItem] = Field(..., description="处理结果列表")
    total_duration: float = Field(..., description="总处理时间")

class BatchSpeakerComparisonItem(BaseModel):
    """批量说话人比较单项结果"""
    file1: str = Field(..., description="第一个文件名")
    file2: str = Field(..., description="第二个文件名")
    success: bool = Field(..., description="是否成功")
    similarity: Optional[float] = Field(None, description="相似度")
    is_same_speaker: Optional[bool] = Field(None, description="是否同一说话人")
    error: Optional[str] = Field(None, description="错误信息")

class BatchSpeakerComparisonResponse(BaseModel):
    """批量说话人比较响应"""
    success: bool = Field(..., description="整体是否成功")
    total_comparisons: int = Field(..., description="总比较次数")
    successful_comparisons: int = Field(..., description="成功的比较次数")
    failed_comparisons: int = Field(..., description="失败的比较次数")
    results: List[BatchSpeakerComparisonItem] = Field(..., description="比较结果列表")
    threshold: float = Field(..., description="相似度阈值")
    model_name: str = Field(..., description="使用的模型名称")

class BatchSpeakerEmbeddingItem(BaseModel):
    """批量说话人嵌入单项结果"""
    filename: str = Field(..., description="文件名")
    success: bool = Field(..., description="是否成功")
    embedding: Optional[List[float]] = Field(None, description="嵌入向量")
    embedding_shape: Optional[List[int]] = Field(None, description="嵌入向量形状")
    embedding_norm: Optional[float] = Field(None, description="嵌入向量范数")
    error: Optional[str] = Field(None, description="错误信息")

class BatchSpeakerEmbeddingResponse(BaseModel):
    """批量说话人嵌入响应"""
    success: bool = Field(..., description="整体是否成功")
    total_files: int = Field(..., description="总文件数")
    successful_files: int = Field(..., description="成功处理的文件数")
    failed_files: int = Field(..., description="失败的文件数")
    results: List[BatchSpeakerEmbeddingItem] = Field(..., description="处理结果列表")
    model_name: str = Field(..., description="使用的模型名称")
    total_duration: float = Field(..., description="总处理时间")

# 人脸识别相关模型
class FaceEmbeddingResponse(BaseModel):
    """人脸嵌入响应"""
    success: bool = Field(..., description="是否成功")
    embedding: List[float] = Field(..., description="人脸嵌入向量")
    embedding_shape: List[int] = Field(..., description="嵌入向量形状")
    embedding_norm: float = Field(..., description="嵌入向量范数")
    face_count: int = Field(..., description="检测到的人脸数量")
    model_name: str = Field(..., description="使用的模型名称")
    file_info: Dict[str, Any] = Field(..., description="文件信息")

class FaceComparisonResponse(BaseModel):
    """人脸比较响应"""
    success: bool = Field(..., description="是否成功")
    similarity: float = Field(..., description="人脸相似度")
    is_same_person: bool = Field(..., description="是否同一个人")
    threshold: float = Field(..., description="相似度阈值")
    model_name: str = Field(..., description="使用的模型名称")
    face_count_1: int = Field(..., description="图片1检测到的人脸数量")
    face_count_2: int = Field(..., description="图片2检测到的人脸数量")
    files: Dict[str, str] = Field(..., description="文件信息")

class BatchFaceEmbeddingItem(BaseModel):
    """批量人脸嵌入单项结果"""
    filename: str = Field(..., description="文件名")
    success: bool = Field(..., description="是否成功")
    embedding: Optional[List[float]] = Field(None, description="嵌入向量")
    embedding_shape: Optional[List[int]] = Field(None, description="嵌入向量形状")
    embedding_norm: Optional[float] = Field(None, description="嵌入向量范数")
    face_count: Optional[int] = Field(None, description="检测到的人脸数量")
    error: Optional[str] = Field(None, description="错误信息")

class BatchFaceEmbeddingResponse(BaseModel):
    """批量人脸嵌入响应"""
    success: bool = Field(..., description="整体是否成功")
    total_files: int = Field(..., description="总文件数")
    successful_files: int = Field(..., description="成功处理的文件数")
    failed_files: int = Field(..., description="失败的文件数")
    results: List[BatchFaceEmbeddingItem] = Field(..., description="处理结果列表")
    model_name: str = Field(..., description="使用的模型名称")
    total_duration: float = Field(..., description="总处理时间")

class BatchFaceComparisonItem(BaseModel):
    """批量人脸比较单项结果"""
    file1: str = Field(..., description="第一个文件名")
    file2: str = Field(..., description="第二个文件名")
    success: bool = Field(..., description="是否成功")
    similarity: Optional[float] = Field(None, description="相似度")
    is_same_person: Optional[bool] = Field(None, description="是否同一个人")
    face_count_1: Optional[int] = Field(None, description="图片1检测到的人脸数量")
    face_count_2: Optional[int] = Field(None, description="图片2检测到的人脸数量")
    error: Optional[str] = Field(None, description="错误信息")

class BatchFaceComparisonResponse(BaseModel):
    """批量人脸比较响应"""
    success: bool = Field(..., description="整体是否成功")
    total_comparisons: int = Field(..., description="总比较次数")
    successful_comparisons: int = Field(..., description="成功的比较次数")
    failed_comparisons: int = Field(..., description="失败的比较次数")
    results: List[BatchFaceComparisonItem] = Field(..., description="比较结果列表")
    threshold: float = Field(..., description="相似度阈值")
    model_name: str = Field(..., description="使用的模型名称")
