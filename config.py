import os
from typing import Dict, Any
from pydantic_settings import BaseSettings

class Settings(BaseSettings):
    """应用配置"""
    
    # API配置
    app_name: str = "音频转文字API"
    app_version: str = "1.0.0"
    debug: bool = False
    
    # Whisper模型配置
    whisper_model: str = "small"  # 可选: tiny, base, small, medium, large
    default_language: str = "en"  # 默认语言
    
    # 文件上传配置
    max_file_size: int = 50 * 1024 * 1024  # 50MB
    allowed_extensions: list = [".wav", ".mp3", ".m4a", ".flac", ".ogg"]
    allowed_image_extensions: list = [".jpg", ".jpeg", ".png", ".bmp", ".tiff", ".webp"]
    upload_dir: str = "uploads"

    # 人脸识别配置
    face_model_name: str = "buffalo_l"  # InsightFace模型名称
    face_det_size: tuple = (640, 640)   # 人脸检测分辨率
    face_ctx_id: int = 0                # GPU设备ID，-1为CPU
    face_similarity_threshold: float = 0.35  # 人脸相似度阈值
    
    # 服务器配置
    host: str = "0.0.0.0"
    port: int = 9000
    
    class Config:
        env_file = ".env"

# 数字映射配置
DIGIT_MAP: Dict[str, str] = {
    # 英文数字映射
    "zero": "0", "one": "1", "two": "2", "three": "3", "four": "4",
    "five": "5", "six": "6", "seven": "7", "eight": "8", "nine": "9",
    
    # 中文数字映射
    "零": "0", "一": "1", "二": "2", "三": "3", "四": "4",
    "五": "5", "六": "6", "七": "7", "八": "8", "九": "9",
    "十": "10", "百": "100", "千": "1000", "万": "10000"
}

settings = Settings()
