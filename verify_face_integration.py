#!/usr/bin/env python3
"""
人脸识别集成验证脚本
检查所有必要的文件和配置是否正确
"""

import os
import sys
import importlib.util
from pathlib import Path

def check_file_exists(file_path, description):
    """检查文件是否存在"""
    if os.path.exists(file_path):
        print(f"✅ {description}: {file_path}")
        return True
    else:
        print(f"❌ {description}: {file_path} (文件不存在)")
        return False

def check_import(module_name, description):
    """检查模块是否可以导入"""
    try:
        __import__(module_name)
        print(f"✅ {description}: {module_name}")
        return True
    except ImportError as e:
        print(f"❌ {description}: {module_name} (导入失败: {e})")
        return False

def check_requirements():
    """检查requirements.txt中的人脸识别依赖"""
    print("\n🔍 检查requirements.txt...")
    
    required_packages = [
        "opencv-python",
        "insightface", 
        "onnxruntime"
    ]
    
    if not os.path.exists("requirements.txt"):
        print("❌ requirements.txt 文件不存在")
        return False
    
    with open("requirements.txt", "r") as f:
        content = f.read()
    
    all_found = True
    for package in required_packages:
        if package in content:
            print(f"✅ 依赖包: {package}")
        else:
            print(f"❌ 依赖包: {package} (未在requirements.txt中找到)")
            all_found = False
    
    return all_found

def check_config():
    """检查config.py中的人脸识别配置"""
    print("\n🔍 检查config.py配置...")
    
    try:
        from config import settings
        
        required_attrs = [
            "face_model_name",
            "face_det_size", 
            "face_ctx_id",
            "face_similarity_threshold",
            "allowed_image_extensions"
        ]
        
        all_found = True
        for attr in required_attrs:
            if hasattr(settings, attr):
                value = getattr(settings, attr)
                print(f"✅ 配置项: {attr} = {value}")
            else:
                print(f"❌ 配置项: {attr} (未找到)")
                all_found = False
        
        return all_found
        
    except Exception as e:
        print(f"❌ 配置检查失败: {e}")
        return False

def check_models():
    """检查models.py中的人脸识别模型"""
    print("\n🔍 检查models.py...")
    
    try:
        from models import (
            FaceEmbeddingResponse,
            FaceComparisonResponse,
            BatchFaceEmbeddingResponse,
            BatchFaceComparisonResponse
        )
        
        print("✅ 人脸识别数据模型导入成功")
        return True
        
    except ImportError as e:
        print(f"❌ 人脸识别数据模型导入失败: {e}")
        return False

def check_main_integration():
    """检查main.py中的人脸识别集成"""
    print("\n🔍 检查main.py集成...")
    
    if not os.path.exists("main.py"):
        print("❌ main.py 文件不存在")
        return False
    
    with open("main.py", "r") as f:
        content = f.read()
    
    required_elements = [
        "from insightface.app import FaceAnalysis",
        "face_app = FaceAnalysis",
        "/face/embedding",
        "/face/compare",
        "/batch/face/embedding",
        "/batch/face/compare",
        "validate_image_file",
        "get_face_embeddings",
        "cosine_similarity"
    ]
    
    all_found = True
    for element in required_elements:
        if element in content:
            print(f"✅ 代码元素: {element}")
        else:
            print(f"❌ 代码元素: {element} (未在main.py中找到)")
            all_found = False
    
    return all_found

def main():
    """主验证函数"""
    print("🚀 人脸识别集成验证")
    print("=" * 50)
    
    all_checks_passed = True
    
    # 检查核心文件
    print("\n📁 检查核心文件...")
    files_to_check = [
        ("main.py", "主应用文件"),
        ("config.py", "配置文件"),
        ("models.py", "数据模型文件"),
        ("requirements.txt", "依赖文件")
    ]
    
    for file_path, description in files_to_check:
        if not check_file_exists(file_path, description):
            all_checks_passed = False
    
    # 检查测试文件
    print("\n🧪 检查测试文件...")
    test_files = [
        ("test/test_face_api.py", "HTTP API测试脚本"),
        ("test_face_simple.py", "简单功能测试脚本")
    ]
    
    for file_path, description in test_files:
        check_file_exists(file_path, description)
    
    # 检查文档文件
    print("\n📚 检查文档文件...")
    doc_files = [
        ("docs/FACE_API_USAGE.md", "人脸识别API使用指南"),
        ("docs/FACE_RECOGNITION_INTEGRATION.md", "集成说明文档")
    ]
    
    for file_path, description in doc_files:
        check_file_exists(file_path, description)
    
    # 检查脚本文件
    print("\n🔧 检查脚本文件...")
    script_files = [
        ("scripts/install_face_deps.sh", "依赖安装脚本")
    ]
    
    for file_path, description in script_files:
        check_file_exists(file_path, description)
    
    # 检查依赖
    if not check_requirements():
        all_checks_passed = False
    
    # 检查配置
    if not check_config():
        all_checks_passed = False
    
    # 检查模型
    if not check_models():
        all_checks_passed = False
    
    # 检查主文件集成
    if not check_main_integration():
        all_checks_passed = False
    
    # 检查Python依赖
    print("\n📦 检查Python依赖...")
    python_deps = [
        ("cv2", "OpenCV"),
        ("numpy", "NumPy"),
        ("fastapi", "FastAPI")
    ]
    
    for module, description in python_deps:
        if not check_import(module, description):
            all_checks_passed = False
    
    # 尝试导入InsightFace (可选)
    print("\n🔍 检查可选依赖...")
    try:
        import insightface
        print(f"✅ InsightFace: 版本 {insightface.__version__}")
    except ImportError:
        print("⚠️  InsightFace: 未安装 (运行 ./scripts/install_face_deps.sh 安装)")
    
    try:
        import onnxruntime
        print(f"✅ ONNX Runtime: 版本 {onnxruntime.__version__}")
    except ImportError:
        print("⚠️  ONNX Runtime: 未安装")
    
    # 总结
    print("\n" + "=" * 50)
    if all_checks_passed:
        print("🎉 所有核心检查通过！人脸识别功能已成功集成")
        print("\n📝 下一步:")
        print("1. 安装人脸识别依赖: ./scripts/install_face_deps.sh")
        print("2. 启动服务: python main.py")
        print("3. 测试功能: python test/test_face_api.py")
        print("4. 查看文档: docs/FACE_API_USAGE.md")
    else:
        print("❌ 部分检查失败，请检查上述错误并修复")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
