#!/usr/bin/env python3
"""
直接测试人脸识别错误修复
不依赖HTTP API，直接调用函数
"""

import os
import sys
import cv2
import numpy as np
import traceback
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, '.')

def test_image_loading():
    """测试图片加载"""
    image_path = "4BDA079063B9AB1C4C82B024339B2A19.jpeg"
    
    print(f"🔍 测试图片加载: {image_path}")
    
    if not os.path.exists(image_path):
        print(f"❌ 文件不存在: {image_path}")
        return None
    
    try:
        # 使用OpenCV加载图片
        img = cv2.imread(image_path)
        if img is None:
            print(f"❌ OpenCV无法加载图片")
            return None
        
        print(f"✅ 图片加载成功")
        print(f"   - 图片形状: {img.shape}")
        print(f"   - 图片类型: {img.dtype}")
        print(f"   - 文件大小: {os.path.getsize(image_path)} bytes")
        
        return img
        
    except Exception as e:
        print(f"❌ 图片加载异常: {str(e)}")
        traceback.print_exc()
        return None

def test_text_orientation_detection(img):
    """测试文本方向检测函数"""
    print(f"\n🔍 测试文本方向检测函数")
    
    try:
        # 导入必要的函数
        from main import detect_text_orientation
        
        print("✅ 成功导入 detect_text_orientation 函数")
        
        # 测试函数
        angle = detect_text_orientation(img)
        print(f"✅ 文本方向检测成功")
        print(f"   - 检测到的角度: {angle}度")
        
        return True
        
    except Exception as e:
        print(f"❌ 文本方向检测失败: {str(e)}")
        traceback.print_exc()
        return False

def test_face_orientation_detection(img):
    """测试人脸方向检测函数"""
    print(f"\n🔍 测试人脸方向检测函数")
    
    try:
        # 导入必要的函数
        from main import detect_face_orientation, init_face_app
        
        print("✅ 成功导入人脸检测函数")
        
        # 初始化人脸识别应用
        print("🔄 初始化人脸识别模型...")
        success = init_face_app()
        if not success:
            print("❌ 人脸识别模型初始化失败")
            return False
        
        print("✅ 人脸识别模型初始化成功")
        
        # 测试函数
        angle = detect_face_orientation(img)
        print(f"✅ 人脸方向检测成功")
        print(f"   - 检测到的角度: {angle}度")
        
        return True
        
    except Exception as e:
        print(f"❌ 人脸方向检测失败: {str(e)}")
        traceback.print_exc()
        return False

def test_auto_correct_orientation(img):
    """测试自动方向矫正函数"""
    print(f"\n🔍 测试自动方向矫正函数")
    
    try:
        # 导入必要的函数
        from main import auto_correct_orientation
        
        print("✅ 成功导入 auto_correct_orientation 函数")
        
        # 测试函数
        corrected_img, rotation_angle = auto_correct_orientation(img)
        print(f"✅ 自动方向矫正成功")
        print(f"   - 矫正角度: {rotation_angle}度")
        print(f"   - 矫正后图片形状: {corrected_img.shape}")
        
        return True, corrected_img
        
    except Exception as e:
        print(f"❌ 自动方向矫正失败: {str(e)}")
        traceback.print_exc()
        return False, None

def test_face_embedding_extraction(img):
    """测试人脸embedding提取"""
    print(f"\n🔍 测试人脸embedding提取")
    
    try:
        # 导入必要的函数
        from main import get_face_embeddings_with_auto_correction
        
        print("✅ 成功导入人脸embedding提取函数")
        
        # 测试函数
        embeddings, face_count, faces = get_face_embeddings_with_auto_correction(img)
        print(f"✅ 人脸embedding提取成功")
        print(f"   - 检测到人脸数量: {face_count}")
        print(f"   - 提取到embedding数量: {len(embeddings)}")
        
        if embeddings:
            print(f"   - 第一个embedding形状: {embeddings[0].shape}")
            print(f"   - 第一个embedding类型: {type(embeddings[0])}")
            print(f"   - 第一个embedding范数: {np.linalg.norm(embeddings[0]):.4f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 人脸embedding提取失败: {str(e)}")
        traceback.print_exc()
        return False

def test_hough_lines_directly():
    """直接测试霍夫变换"""
    print(f"\n🔍 直接测试霍夫变换")
    
    try:
        # 创建一个简单的测试图片
        test_img = np.zeros((100, 100, 3), dtype=np.uint8)
        # 画一些线条
        cv2.line(test_img, (10, 10), (90, 90), (255, 255, 255), 2)
        cv2.line(test_img, (10, 90), (90, 10), (255, 255, 255), 2)
        
        gray = cv2.cvtColor(test_img, cv2.COLOR_BGR2GRAY)
        edges = cv2.Canny(gray, 50, 150, apertureSize=3)
        lines = cv2.HoughLines(edges, 1, np.pi/180, threshold=50)
        
        print(f"✅ 霍夫变换执行成功")
        print(f"   - 检测到的线条数量: {len(lines) if lines is not None else 0}")
        
        if lines is not None and len(lines) > 0:
            print(f"   - lines类型: {type(lines)}")
            print(f"   - lines形状: {lines.shape}")
            print(f"   - 第一条线: {lines[0]}")
            print(f"   - 第一条线形状: {lines[0].shape}")
            
            # 测试解包
            for i, line in enumerate(lines[:3]):
                try:
                    if line.ndim == 2 and line.shape[1] >= 2:
                        rho, theta = line[0][:2]
                        print(f"   - 线条{i+1}: rho={rho:.2f}, theta={theta:.4f}")
                    elif line.ndim == 1 and len(line) >= 2:
                        rho, theta = line[:2]
                        print(f"   - 线条{i+1}: rho={rho:.2f}, theta={theta:.4f}")
                    else:
                        print(f"   - 线条{i+1}: 未知格式 {line}")
                except Exception as e:
                    print(f"   - 线条{i+1}: 解包失败 {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 霍夫变换测试失败: {str(e)}")
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 人脸识别错误修复直接测试")
    print("=" * 60)
    print("直接调用函数，不依赖HTTP API")
    print("=" * 60)
    
    # 1. 测试霍夫变换
    print(f"\n{'='*60}")
    print("🔧 测试霍夫变换基础功能")
    print(f"{'='*60}")
    test_hough_lines_directly()
    
    # 2. 测试图片加载
    print(f"\n{'='*60}")
    print("📸 测试图片加载")
    print(f"{'='*60}")
    img = test_image_loading()
    if img is None:
        print("❌ 图片加载失败，无法继续测试")
        return
    
    # 3. 测试文本方向检测
    print(f"\n{'='*60}")
    print("📝 测试文本方向检测")
    print(f"{'='*60}")
    test_text_orientation_detection(img)
    
    # 4. 测试人脸方向检测
    print(f"\n{'='*60}")
    print("👤 测试人脸方向检测")
    print(f"{'='*60}")
    test_face_orientation_detection(img)
    
    # 5. 测试自动方向矫正
    print(f"\n{'='*60}")
    print("🔄 测试自动方向矫正")
    print(f"{'='*60}")
    success, corrected_img = test_auto_correct_orientation(img)
    
    # 6. 测试人脸embedding提取
    print(f"\n{'='*60}")
    print("🎯 测试人脸embedding提取")
    print(f"{'='*60}")
    test_face_embedding_extraction(img)
    
    print(f"\n{'='*60}")
    print("📊 测试完成")
    print(f"{'='*60}")
    print("如果所有测试都通过，说明错误修复成功！")
    print("如果仍有错误，请查看详细的错误信息进行进一步修复。")

if __name__ == "__main__":
    main()
