# 🎵 AI音频处理与人脸识别API

基于OpenAI Whisper、ModelScope和InsightFace的高性能音频转录、说话人识别和人脸识别API服务。

## ✨ 功能特性

- 🎵 **音频转录**: 支持多种格式和语言的音频转文字
- 🎤 **说话人识别**: 说话人嵌入提取和相似度比较
- 👤 **人脸识别**: 人脸嵌入提取和相似度比较
- 🚀 **批量处理**: 支持批量音频转录、说话人分析和人脸识别
- 🌍 **多语言支持**: 中文、英语、日语、韩语等10+语言
- 📊 **详细统计**: 提供置信度、时长等详细信息
- 🔧 **易于部署**: Docker支持，一键启动

## 🚀 快速开始

### 安装和启动

```bash
# 克隆项目
git clone <repository-url>
cd ai-api

# 安装依赖
pip install -r requirements.txt

# 启动服务
./scripts/start.sh
```

### 访问服务

- 🌐 **API服务**: http://localhost:9000
- 📚 **API文档**: http://localhost:9000/docs
- ❤️ **健康检查**: http://localhost:9000/health

### 基本使用

```bash
# 音频转录
curl -X POST "http://localhost:9000/transcribe" \
  -F "file=@audio.wav" -F "language=zh"

# 说话人比较
curl -X POST "http://localhost:9000/speaker/compare" \
  -F "file1=@speaker1.wav" -F "file2=@speaker2.wav"

# 人脸识别
curl -X POST "http://localhost:9000/face/embedding" \
  -F "file=@face.jpg"

# 人脸比较
curl -X POST "http://localhost:9000/face/compare" \
  -F "file1=@face1.jpg" -F "file2=@face2.jpg"
```

## 📋 API接口概览

| 功能 | 接口 | 说明 |
|------|------|------|
| 音频转录 | `POST /transcribe` | 单文件音频转文字 |
| 批量转录 | `POST /batch/transcribe` | 多文件批量转录 |
| 说话人嵌入 | `POST /speaker/embedding` | 提取说话人特征 |
| 批量嵌入 | `POST /batch/speaker/embedding` | 批量提取特征 |
| 说话人比较 | `POST /speaker/compare` | 比较两个说话人 |
| 批量比较 | `POST /batch/speaker/compare` | 批量说话人比较 |
| 人脸嵌入 | `POST /face/embedding` | 提取人脸特征向量 |
| 批量人脸嵌入 | `POST /batch/face/embedding` | 批量提取人脸特征 |
| 人脸比较 | `POST /face/compare` | 比较两张人脸 |
| 批量人脸比较 | `POST /batch/face/compare` | 批量人脸比较 |

## 🎯 支持格式

- **音频格式**: WAV, MP3, M4A, FLAC, OGG
- **图片格式**: JPG, JPEG, PNG, BMP, TIFF, WebP
- **语言支持**: 中文、英语、日语、韩语、法语、德语、西班牙语、俄语、阿拉伯语、印地语

## 📖 文档

- 📖 [完整API文档](docs/API_DOCUMENTATION.md) - 详细的API使用说明
- 📋 [项目状态](docs/PROJECT_STATUS.md) - 开发进度和功能状态
- 📝 [使用说明](docs/USAGE_INSTRUCTIONS.md) - 详细的使用指南
- 👤 [人脸识别API使用指南](docs/FACE_API_USAGE.md) - 人脸识别功能详细说明

## 🧪 测试

```bash
# 运行所有测试
./scripts/test.sh

# API功能测试
./scripts/test.sh api

# 批量处理测试
python test/test_batch_api.py

# 人脸识别测试
python test/test_face_api.py

# 人脸识别功能测试（不依赖HTTP API）
python test_face_simple.py
```

## 🐳 Docker部署

```bash
# 构建和运行
docker build -t ai-api .
docker run -p 9000:8000 ai-api
```

## ⚡ 性能指标

- **转录速度**: ~1-2x实时速度 (CPU)
- **说话人识别**: <1秒/比较
- **人脸识别**: <0.5秒/张图片 (GPU), <2秒/张图片 (CPU)
- **内存使用**: ~2-4GB (CPU), ~4-6GB (GPU)
- **并发支持**: 批量处理

## 🛠️ 技术栈

- **Web框架**: FastAPI + Uvicorn
- **音频转录**: OpenAI Whisper  
- **说话人识别**: ModelScope ERes2Net
- **音频处理**: librosa + soundfile

## 📄 许可证

MIT License

---

**🎉 开始使用**: 查看 [完整文档](docs/API_DOCUMENTATION.md) 了解更多详细信息！
