#!/bin/bash

# 人脸识别依赖安装脚本

echo "🚀 开始安装人脸识别依赖..."

# 检查Python版本
python_version=$(python3 --version 2>&1 | awk '{print $2}' | cut -d. -f1,2)
echo "📋 检测到Python版本: $python_version"

# 检查是否有GPU支持
if command -v nvidia-smi &> /dev/null; then
    echo "🎮 检测到NVIDIA GPU，将安装GPU版本"
    GPU_SUPPORT=true
else
    echo "💻 未检测到NVIDIA GPU，将安装CPU版本"
    GPU_SUPPORT=false
fi

# 安装基础依赖
echo "📦 安装基础依赖..."
pip install opencv-python==********

# 安装ONNX Runtime
if [ "$GPU_SUPPORT" = true ]; then
    echo "📦 安装ONNX Runtime GPU版本..."
    pip install onnxruntime-gpu==1.16.3
else
    echo "📦 安装ONNX Runtime CPU版本..."
    pip install onnxruntime==1.16.3
fi

# 安装InsightFace
echo "📦 安装InsightFace..."
pip install insightface==0.7.3

# 验证安装
echo "🔍 验证安装..."
python3 -c "
import cv2
print('✅ OpenCV安装成功，版本:', cv2.__version__)

try:
    import onnxruntime as ort
    print('✅ ONNX Runtime安装成功，版本:', ort.__version__)
    
    # 检查可用的执行提供者
    providers = ort.get_available_providers()
    print('📋 可用的执行提供者:', providers)
    
    if 'CUDAExecutionProvider' in providers:
        print('🎮 GPU加速可用')
    else:
        print('💻 仅CPU模式可用')
        
except ImportError as e:
    print('❌ ONNX Runtime导入失败:', e)

try:
    import insightface
    print('✅ InsightFace安装成功，版本:', insightface.__version__)
except ImportError as e:
    print('❌ InsightFace导入失败:', e)
"

# 测试人脸识别功能
echo "🧪 测试人脸识别功能..."
if python3 test_face_simple.py > /dev/null 2>&1; then
    echo "✅ 人脸识别功能测试通过"
else
    echo "⚠️  人脸识别功能测试失败，请检查依赖安装"
    echo "💡 提示: 可能需要下载模型文件，首次运行会自动下载"
fi

echo "🎉 人脸识别依赖安装完成！"
echo ""
echo "📝 使用说明:"
echo "1. 启动API服务: python main.py"
echo "2. 查看API文档: http://localhost:9000/docs"
echo "3. 运行测试: python test/test_face_api.py"
echo "4. 查看详细文档: docs/FACE_API_USAGE.md"
