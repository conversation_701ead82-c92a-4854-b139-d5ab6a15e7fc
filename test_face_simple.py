#!/usr/bin/env python3
"""
简单的人脸识别功能测试脚本
直接调用人脸识别函数，不通过HTTP API
"""

import os
import cv2
import numpy as np
from typing import Tuple, List

def l2_normalize(v: np.ndarray, eps: float = 1e-12) -> np.ndarray:
    return v / (np.linalg.norm(v) + eps)

def cosine_similarity(a: np.ndarray, b: np.ndarray) -> float:
    a = l2_normalize(a)
    b = l2_normalize(b)
    return float(np.dot(a, b))

def init_insightface(model_name: str = "buffalo_l",
                     det_size: Tuple[int, int] = (640, 640),
                     ctx_id: int = 0):
    """
    初始化 InsightFace 应用：
    - model_name: "buffalo_l" 为较高精度组合（包含人脸检测+识别）
    - det_size:   检测分辨率，视显存/内存情况调整
    - ctx_id:     0 为 GPU 0；若无 GPU 可设为 -1 使用 CPU
    """
    try:
        from insightface.app import FaceAnalysis
        app = FaceAnalysis(name=model_name, allowed_modules=["detection", "recognition"])
        app.prepare(ctx_id=ctx_id, det_size=det_size)
        return app
    except ImportError:
        print("❌ InsightFace 未安装，请运行: pip install insightface")
        return None
    except Exception as e:
        print(f"❌ InsightFace 初始化失败: {e}")
        print("尝试使用CPU模式...")
        try:
            app = FaceAnalysis(name=model_name, allowed_modules=["detection", "recognition"])
            app.prepare(ctx_id=-1, det_size=det_size)
            return app
        except Exception as e2:
            print(f"❌ CPU模式也失败: {e2}")
            return None

def read_image(path: str) -> np.ndarray:
    img = cv2.imread(path)
    if img is None:
        raise FileNotFoundError(f"Cannot read image: {path}")
    return img

def get_face_embeddings(app, img: np.ndarray) -> List[np.ndarray]:
    """
    对输入图像进行人脸检测/对齐，返回每张人脸的 embedding（512D）。
    """
    faces = app.get(img)  # 自动对齐 + 识别向量
    embeddings = []
    for f in faces:
        # f.normed_embedding 已经是L2归一化过的向量（有的版本叫 embedding，需要自己 normalize）
        emb = getattr(f, "normed_embedding", None)
        if emb is None:
            emb = f.embedding
            emb = l2_normalize(emb)
        embeddings.append(emb.astype(np.float32))
    return embeddings

def pick_primary_face(embeddings: List[np.ndarray]) -> np.ndarray:
    """
    根据需要挑选一张脸的 embedding。
    这里简单选择"第一张检测到的人脸"。也可改成按最大框面积/人脸质量分数挑选。
    """
    if not embeddings:
        raise ValueError("No face detected.")
    return embeddings[0]

def compare_two_images(app, img_path1: str, img_path2: str, threshold: float = 0.35) -> Tuple[float, bool]:
    """
    比对两张图片是否为同一人。
    """
    img1 = read_image(img_path1)
    img2 = read_image(img_path2)

    embs1 = get_face_embeddings(app, img1)
    embs2 = get_face_embeddings(app, img2)

    emb1 = pick_primary_face(embs1)
    emb2 = pick_primary_face(embs2)

    sim = cosine_similarity(emb1, emb2)
    is_same = sim >= threshold
    return sim, is_same

def test_face_recognition():
    """测试人脸识别功能"""
    print("🚀 测试人脸识别功能")
    print("=" * 50)
    
    # 初始化模型
    print("🔄 正在初始化InsightFace模型...")
    app = init_insightface()
    
    if app is None:
        print("❌ 模型初始化失败，无法继续测试")
        return
    
    print("✅ 模型初始化成功")
    
    # 测试图片路径
    test_images = [
        "test/face1.jpg",
        "test/face2.jpg",
        "test/face3.jpg"
    ]
    
    print("\n📝 测试说明:")
    print("请将测试图片放在 test/ 目录下，命名为:")
    for img in test_images:
        print(f"   - {img}")
    
    # 检查测试图片
    available_images = []
    for img_path in test_images:
        if os.path.exists(img_path):
            available_images.append(img_path)
            print(f"✅ 找到图片: {img_path}")
        else:
            print(f"⚠️  图片不存在: {img_path}")
    
    if len(available_images) == 0:
        print("\n❌ 没有找到测试图片，请添加图片后重新运行")
        return
    
    # 测试单张图片的embedding提取
    print(f"\n🔍 测试人脸嵌入提取...")
    for img_path in available_images:
        try:
            img = read_image(img_path)
            embeddings = get_face_embeddings(app, img)
            
            if len(embeddings) > 0:
                emb = embeddings[0]
                norm = np.linalg.norm(emb)
                print(f"✅ {img_path}: 检测到 {len(embeddings)} 张人脸，嵌入维度: {emb.shape}, 范数: {norm:.4f}")
            else:
                print(f"❌ {img_path}: 未检测到人脸")
                
        except Exception as e:
            print(f"❌ {img_path}: 处理失败 - {str(e)}")
    
    # 测试人脸比较
    if len(available_images) >= 2:
        print(f"\n🔍 测试人脸比较...")
        for i in range(len(available_images)):
            for j in range(i + 1, len(available_images)):
                img1 = available_images[i]
                img2 = available_images[j]
                
                try:
                    similarity, is_same = compare_two_images(app, img1, img2)
                    result = "同一人" if is_same else "不同人"
                    print(f"✅ {os.path.basename(img1)} vs {os.path.basename(img2)}: 相似度 {similarity:.4f} ({result})")
                    
                except Exception as e:
                    print(f"❌ {os.path.basename(img1)} vs {os.path.basename(img2)}: 比较失败 - {str(e)}")
    
    print("\n✅ 测试完成")

if __name__ == "__main__":
    test_face_recognition()
